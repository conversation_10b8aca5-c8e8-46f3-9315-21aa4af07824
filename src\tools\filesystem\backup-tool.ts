import { Logger } from 'winston';
import { z } from 'zod';
import { promises as fs } from 'fs';
import path from 'path';
import { BaseTool, <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentContext, ToolError } from '@/types';

const BackupToolSchema = z.discriminatedUnion('operation', [
  z.object({
    operation: z.literal('create'),
    path: z.string(),
    description: z.string().optional(),
  }),
  z.object({
    operation: z.literal('restore'),
    backupId: z.string(),
    targetPath: z.string().optional(),
  }),
  z.object({
    operation: z.literal('list'),
    path: z.string().optional(),
    limit: z.number().min(1).max(100).default(20),
  }),
  z.object({
    operation: z.literal('delete'),
    backupId: z.string(),
  }),
  z.object({
    operation: z.literal('cleanup'),
    maxAge: z.number().min(1).default(7), // days
    dryRun: z.boolean().default(false),
  }),
  z.object({
    operation: z.literal('info'),
    backupId: z.string(),
  }),
]);

type BackupToolParams = z.infer<typeof BackupToolSchema>;

interface BackupMetadata {
  id: string;
  originalPath: string;
  backupPath: string;
  description: string;
  createdAt: Date;
  size: number;
  checksum: string;
}

export class BackupTool extends BaseTool<BackupToolParams> {
  public readonly name = 'backup_manager';
  public readonly description = 'Advanced backup and restore operations with metadata tracking';
  public readonly parameters = BackupToolSchema;
  public readonly parallel = true;
  public readonly dangerous = false;

  private readonly logger: Logger;
  private readonly backupDir: string;
  private readonly metadataFile: string;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.backupDir = path.join(process.cwd(), '.agent-backups');
    this.metadataFile = path.join(this.backupDir, 'metadata.json');
    this.initializeBackupSystem();
  }

  private async initializeBackupSystem(): Promise<void> {
    try {
      await fs.mkdir(this.backupDir, { recursive: true });
      
      // Initialize metadata file if it doesn't exist
      try {
        await fs.access(this.metadataFile);
      } catch {
        await fs.writeFile(this.metadataFile, JSON.stringify([], null, 2));
      }
    } catch (error) {
      this.logger.warn('Failed to initialize backup system', { error });
    }
  }

  protected async executeTyped(params: BackupToolParams, context: AgentContext): Promise<ToolResult> {
    this.logger.info('Executing backup operation', { operation: params.operation });

    try {
      switch (params.operation) {
        case 'create':
          return await this.createBackup(params, context);
        case 'restore':
          return await this.restoreBackup(params, context);
        case 'list':
          return await this.listBackups(params, context);
        case 'delete':
          return await this.deleteBackup(params, context);
        case 'cleanup':
          return await this.cleanupBackups(params, context);
        case 'info':
          return await this.getBackupInfo(params, context);
        default:
          throw new ToolError(`Unknown operation: ${(params as any).operation}`, this.name);
      }
    } catch (error) {
      this.logger.error('Backup operation failed', { error, operation: params.operation });
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  private async createBackup(
    params: Extract<BackupToolParams, { operation: 'create' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    
    if (!(await this.fileExists(fullPath))) {
      throw new ToolError(`File not found: ${fullPath}`, this.name);
    }

    const backupId = this.generateBackupId();
    const backupPath = path.join(this.backupDir, `${backupId}.backup`);
    
    // Copy file to backup location
    await fs.copyFile(fullPath, backupPath);
    
    // Get file stats and checksum
    const stats = await fs.stat(fullPath);
    const checksum = await this.calculateChecksum(fullPath);
    
    // Create metadata entry
    const metadata: BackupMetadata = {
      id: backupId,
      originalPath: fullPath,
      backupPath,
      description: params.description || 'Manual backup',
      createdAt: new Date(),
      size: stats.size,
      checksum,
    };

    await this.saveMetadata(metadata);

    return {
      success: true,
      data: {
        backupId,
        originalPath: fullPath,
        backupPath,
        size: stats.size,
        description: params.description,
      },
    };
  }

  private async restoreBackup(
    params: Extract<BackupToolParams, { operation: 'restore' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const metadata = await this.getBackupMetadata(params.backupId);
    if (!metadata) {
      throw new ToolError(`Backup not found: ${params.backupId}`, this.name);
    }

    const targetPath = params.targetPath 
      ? this.resolvePath(params.targetPath, context.workingDirectory)
      : metadata.originalPath;

    // Verify backup integrity
    const isValid = await this.verifyBackupIntegrity(metadata);
    if (!isValid) {
      throw new ToolError(`Backup integrity check failed: ${params.backupId}`, this.name);
    }

    // Create backup of current file if it exists
    let currentFileBackupId: string | undefined;
    if (await this.fileExists(targetPath)) {
      const currentBackupResult = await this.createBackup(
        { operation: 'create', path: targetPath, description: 'Pre-restore backup' },
        context
      );
      currentFileBackupId = (currentBackupResult.data as { backupId?: string })?.backupId as string;
    }

    // Restore from backup
    await fs.copyFile(metadata.backupPath, targetPath);

    return {
      success: true,
      data: {
        restoredTo: targetPath,
        fromBackup: params.backupId,
        currentFileBackupId,
        originalPath: metadata.originalPath,
      },
    };
  }

  private async listBackups(
    params: Extract<BackupToolParams, { operation: 'list' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const allMetadata = await this.loadAllMetadata();
    
    let filteredMetadata = allMetadata;
    if (params.path) {
      const fullPath = this.resolvePath(params.path, context.workingDirectory);
      filteredMetadata = allMetadata.filter(m => m.originalPath === fullPath);
    }

    const sortedMetadata = filteredMetadata
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, params.limit);

    return {
      success: true,
      data: {
        backups: sortedMetadata.map(m => ({
          id: m.id,
          originalPath: m.originalPath,
          description: m.description,
          createdAt: m.createdAt,
          size: m.size,
        })),
        totalBackups: filteredMetadata.length,
        showing: sortedMetadata.length,
      },
    };
  }

  private async deleteBackup(
    params: Extract<BackupToolParams, { operation: 'delete' }>,
    _context: AgentContext
  ): Promise<ToolResult> {
    const metadata = await this.getBackupMetadata(params.backupId);
    if (!metadata) {
      throw new ToolError(`Backup not found: ${params.backupId}`, this.name);
    }

    // Delete backup file
    await fs.unlink(metadata.backupPath);
    
    // Remove from metadata
    await this.removeMetadata(params.backupId);

    return {
      success: true,
      data: {
        deletedBackupId: params.backupId,
        originalPath: metadata.originalPath,
      },
    };
  }

  private async cleanupBackups(
    params: Extract<BackupToolParams, { operation: 'cleanup' }>,
    _context: AgentContext
  ): Promise<ToolResult> {
    const allMetadata = await this.loadAllMetadata();
    const cutoffDate = new Date(Date.now() - params.maxAge * 24 * 60 * 60 * 1000);
    
    const oldBackups = allMetadata.filter(m => m.createdAt < cutoffDate);
    
    if (params.dryRun) {
      return {
        success: true,
        data: {
          dryRun: true,
          wouldDelete: oldBackups.length,
          backups: oldBackups.map(m => ({
            id: m.id,
            originalPath: m.originalPath,
            createdAt: m.createdAt,
            size: m.size,
          })),
        },
      };
    }

    let deletedCount = 0;
    for (const backup of oldBackups) {
      try {
        await fs.unlink(backup.backupPath);
        await this.removeMetadata(backup.id);
        deletedCount++;
      } catch (error) {
        this.logger.warn('Failed to delete backup during cleanup', { 
          backupId: backup.id, 
          error 
        });
      }
    }

    return {
      success: true,
      data: {
        deletedCount,
        maxAge: params.maxAge,
        cutoffDate,
      },
    };
  }

  private async getBackupInfo(
    params: Extract<BackupToolParams, { operation: 'info' }>,
    _context: AgentContext
  ): Promise<ToolResult> {
    const metadata = await this.getBackupMetadata(params.backupId);
    if (!metadata) {
      throw new ToolError(`Backup not found: ${params.backupId}`, this.name);
    }

    const isValid = await this.verifyBackupIntegrity(metadata);
    const backupStats = await fs.stat(metadata.backupPath);

    return {
      success: true,
      data: {
        ...metadata,
        integrityValid: isValid,
        actualSize: backupStats.size,
        lastModified: backupStats.mtime,
      },
    };
  }

  // Helper methods would continue here...
  private resolvePath(filePath: string, workingDirectory: string): string {
    return path.isAbsolute(filePath) ? filePath : path.resolve(workingDirectory, filePath);
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async calculateChecksum(filePath: string): Promise<string> {
    // Simple checksum implementation - in production, use crypto.createHash
    const content = await fs.readFile(filePath);
    return content.length.toString(16);
  }

  private async saveMetadata(metadata: BackupMetadata): Promise<void> {
    const allMetadata = await this.loadAllMetadata();
    allMetadata.push(metadata);
    await fs.writeFile(this.metadataFile, JSON.stringify(allMetadata, null, 2));
  }

  private async loadAllMetadata(): Promise<BackupMetadata[]> {
    try {
      const data = await fs.readFile(this.metadataFile, 'utf8');
      return JSON.parse(data).map((m: any) => ({
        ...m,
        createdAt: new Date(m.createdAt),
      }));
    } catch {
      return [];
    }
  }

  private async getBackupMetadata(backupId: string): Promise<BackupMetadata | undefined> {
    const allMetadata = await this.loadAllMetadata();
    return allMetadata.find(m => m.id === backupId);
  }

  private async removeMetadata(backupId: string): Promise<void> {
    const allMetadata = await this.loadAllMetadata();
    const filteredMetadata = allMetadata.filter(m => m.id !== backupId);
    await fs.writeFile(this.metadataFile, JSON.stringify(filteredMetadata, null, 2));
  }

  private async verifyBackupIntegrity(metadata: BackupMetadata): Promise<boolean> {
    try {
      const currentChecksum = await this.calculateChecksum(metadata.backupPath);
      return currentChecksum === metadata.checksum;
    } catch {
      return false;
    }
  }
}
