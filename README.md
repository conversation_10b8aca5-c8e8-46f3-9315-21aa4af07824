# Autonomous Agentic AI-Powered CLI Tool System

A production-ready, autonomous agentic AI-powered CLI tool system built with TypeScript 5.8.3 and Node.js. This tool operates seamlessly across Windows 11 WSL, macOS, and Linux environments with enterprise-grade architecture and advanced agentic capabilities.

## 🚀 Features

### Core Autonomous Agent Features
- **Autonomous Planning & Execution**: Intelligent task decomposition and execution flow
- **Parallel Tool Execution**: Safe concurrent operations when beneficial
- **Intelligent Error Handling**: Automatic recovery mechanisms and retry strategies
- **Function Tool Calling**: Comprehensive shell command execution with full system access
- **Chaining & Workflows**: Multi-tool chaining and autonomous workflow execution

### File System Operations
- **Comprehensive Operations**: Read, write, create, delete, move, copy, chmod, permissions
- **Intelligent File Handling**: Automatic file type detection and safe operations
- **Backup & Rollback**: Automatic backups with rollback capabilities
- **Search & Glob**: Advanced file search with glob patterns and content search

### Session and Context Management
- **Project Discovery**: Automatic indexing of current working directory and project structure
- **Intelligent Context**: Awareness of project files, dependencies, and configuration
- **Persistent Memory**: Context memory that survives CLI session restarts
- **Dynamic Updates**: Real-time context updates as environment changes
- **Session Restoration**: Full context preservation and retrieval

### LLM Provider Integration
- **Multi-Provider Support**: DeepSeek, OpenAI, Ollama, Anthropic Claude, Google Gemini, Mistral
- **Provider Failover**: Automatic failover and load balancing
- **Configurable Models**: Per-provider model selection and configuration
- **Secure Credentials**: Safe API key management and storage
- **Rate Limiting**: Built-in quota management and rate limiting

### User Interface
- **Modern Terminal UI**: Real-time streaming of function tool execution
- **Interactive Progress**: Live progress indicators and status updates
- **Colored Output**: Formatted responses for better readability
- **Command History**: Session management and command history
- **Configurable Verbosity**: Multiple verbosity levels

## 📦 Installation

### Prerequisites
- Node.js 18+ (Latest LTS recommended)
- npm, yarn, or pnpm
- Git (optional, for version control features)

### Install from Source

```bash
# Clone the repository
git clone https://github.com/yourusername/agentic-cli-tool.git
cd agentic-cli-tool

# Install dependencies
npm install

# Build the project
npm run build

# Install globally
npm install -g .
```

### Install from npm (when published)

```bash
npm install -g agentic-cli-tool
```

## 🔧 Configuration

### Initial Setup

```bash
# Start interactive configuration
agent-cli config

# Add LLM providers
agent-cli provider add openai-gpt4 openai gpt-4 --api-key YOUR_API_KEY
agent-cli provider add anthropic-claude anthropic claude-3-sonnet-20240229 --api-key YOUR_API_KEY
agent-cli provider add ollama-local ollama llama2 --base-url http://localhost:11434

# Enable dangerous operations (optional)
agent-cli config set tools.allowDangerous true
```

### Environment Variables

```bash
# LLM Provider API Keys
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export GOOGLE_API_KEY="your-google-api-key"
export MISTRAL_API_KEY="your-mistral-api-key"
export DEEPSEEK_API_KEY="your-deepseek-api-key"

# Optional Configuration
export AGENT_CLI_LOG_LEVEL="info"
export AGENT_CLI_CONFIG_DIR="~/.config/agent-cli"
```

## 🎯 Usage

### Interactive Mode (Default)

```bash
# Start interactive session
agent-cli

# Or specify working directory
agent-cli -d /path/to/project

# Resume previous session
agent-cli --session my-session-id
```

### Single Goal Execution

```bash
# Execute a single goal
agent-cli execute "Analyze this project and create a comprehensive README"

# With options
agent-cli execute "Set up CI/CD pipeline" --dangerous --timeout 600
```

### Example Goals

```bash
# Project Analysis
"Analyze this TypeScript project structure and suggest improvements"
"Find all unused dependencies and create a cleanup plan"
"Generate API documentation from TypeScript interfaces"

# Development Tasks
"Create a new React component with TypeScript and tests"
"Set up ESLint and Prettier configuration for this project"
"Optimize package.json and update outdated dependencies"

# File Operations
"Create backups of all configuration files"
"Find and fix all ESLint errors in JavaScript files"
"Organize project files into proper directory structure"

# System Tasks
"Check system health and installed development tools"
"Set up development environment for Node.js project"
"Create deployment scripts for this application"
```

## 🛠️ Available Commands

### Interactive Commands

```bash
/help           # Show help and available commands
/status         # Show agent status and configuration
/context        # Display current context information
/providers      # List LLM providers and their status
/tools          # Show available tools and capabilities
/history        # View recent action history
/save [name]    # Save current session
/load <name>    # Load saved session
/stop           # Stop current operation
/clear          # Clear terminal screen
/exit           # Exit the CLI
```

### CLI Commands

```bash
# Configuration Management
agent-cli config show                    # Show current configuration
agent-cli config set <key> <value>      # Set configuration value
agent-cli config reset --confirm        # Reset to defaults

# Provider Management
agent-cli provider list                  # List configured providers
agent-cli provider add <name> <type> <model> [options]
agent-cli provider remove <name>        # Remove provider

# Session Management
agent-cli session list                  # List saved sessions
agent-cli session delete <id>           # Delete session

# Health Check
agent-cli health                        # Check system health
```

## 🏗️ Architecture

### Core Components

```
src/
├── core/
│   ├── agent/           # Autonomous agent system
│   ├── context/         # Context management
│   ├── session/         # Session management
│   └── providers/       # LLM provider integrations
├── tools/
│   ├── filesystem/      # File system operations
│   ├── shell/          # Shell command execution
│   └── parallel/       # Parallel execution engine
├── ui/
│   ├── terminal/       # Terminal UI components
│   └── progress/       # Progress indicators
├── config/             # Configuration management
├── utils/              # Utility functions
└── cli.ts              # Main CLI entry point
```

### Key Classes

- **AutonomousAgent**: Core agent with planning and execution capabilities
- **LLMProviderManager**: Multi-provider LLM integration with failover
- **ContextManager**: Persistent context and session management
- **ToolRegistry**: Tool management and execution
- **ConfigManager**: Configuration and credential management

## 🔒 Security Features

### Safe Operations
- Dangerous operations disabled by default
- Automatic file backups before destructive operations
- Comprehensive audit logging
- Command validation and sanitization

### Credential Management
- Secure API key storage
- Environment variable integration
- No credentials in logs or output
- Configurable credential sources

### Access Control
- Configurable tool permissions
- Safe mode for production environments
- Operation timeouts and limits
- Resource usage monitoring

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- utils.test.ts
```

## 📊 Monitoring & Logging

### Log Files
- `agent-cli.log` - General application logs
- `agent-cli-error.log` - Error logs only
- `debug.log` - Debug information (when enabled)

### Log Levels
- `silent` - No output
- `normal` - Standard information
- `verbose` - Detailed information
- `debug` - Full debugging information

### Performance Monitoring
- Operation timing and duration
- Resource usage tracking
- Provider response times
- Tool execution metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Clone and install
git clone https://github.com/yourusername/agentic-cli-tool.git
cd agentic-cli-tool
npm install

# Start development
npm run dev

# Run tests
npm test

# Build
npm run build
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [GitHub Wiki](https://github.com/yourusername/agentic-cli-tool/wiki)
- **Issues**: [GitHub Issues](https://github.com/yourusername/agentic-cli-tool/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/agentic-cli-tool/discussions)
- **Discord**: [Community Discord](https://discord.gg/your-discord-link)

## 🗺️ Roadmap

### Version 1.1
- [ ] Plugin system for custom tools
- [ ] Web interface for remote management
- [ ] Docker container support
- [ ] Advanced workflow templates

### Version 1.2
- [ ] Multi-agent collaboration
- [ ] Cloud provider integrations
- [ ] Advanced analytics dashboard
- [ ] Enterprise SSO integration

### Version 2.0
- [ ] Visual workflow designer
- [ ] AI model fine-tuning
- [ ] Distributed execution
- [ ] Advanced security features

## 🙏 Acknowledgments

- [OpenAI](https://openai.com/) for GPT models
- [Anthropic](https://anthropic.com/) for Claude models
- [Google](https://ai.google.dev/) for Gemini models
- [Mistral AI](https://mistral.ai/) for Mistral models
- [DeepSeek](https://deepseek.com/) for DeepSeek models
- [Ollama](https://ollama.ai/) for local model support

---

**Built with ❤️ for developers who want autonomous AI assistance in their workflow.**
