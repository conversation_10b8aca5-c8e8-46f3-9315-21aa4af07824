import chalk from 'chalk';
import { Logger } from 'winston';
import { ConfigManager } from '@config/config-manager';
import { EventEmitter } from 'events';
import { AgentTask, AgentPlan, ToolResult } from '@/types';

export class TerminalUI extends EventEmitter {
  private readonly configManager: ConfigManager;
  private readonly colors: boolean;
  private activeSpinners: Map<string, NodeJS.Timeout> = new Map();
  private streamingOutput: boolean = false;

  constructor(_logger: Logger, configManager: ConfigManager) {
    super();
    // Logger is passed but not used in this class
    this.configManager = configManager;
    this.colors = configManager.getUIConfig().colors;
  }

  showWelcome(): void {
    const welcome = `
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    🤖 Autonomous Agentic AI-Powered CLI Tool System                         ║
║                                                                              ║
║    Production-ready autonomous agent with advanced capabilities             ║
║    • Multi-provider LLM support with failover                              ║
║    • Comprehensive file system operations                                   ║
║    • Parallel tool execution and chaining                                   ║
║    • Persistent context and session management                              ║
║    • Cross-platform compatibility (Windows, macOS, Linux)                  ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

${this.colorize('Welcome to the Autonomous Agent CLI!', 'cyan')}

${this.colorize('Getting Started:', 'yellow')}
• Type your goal in natural language and press Enter
• Use ${this.colorize('/help', 'green')} to see available commands
• Use ${this.colorize('/status', 'green')} to check agent status
• Use ${this.colorize('/exit', 'green')} to quit

${this.colorize('Examples:', 'blue')}
• "Analyze this project structure and create a README"
• "Find all TypeScript files and check for unused imports"
• "Create a backup of all configuration files"
• "Set up a new Node.js project with TypeScript and Jest"

${this.colorize('Safety Features:', 'magenta')}
• Dangerous operations are disabled by default
• All file operations can be backed up automatically
• Session state is preserved and can be restored
• Comprehensive logging for audit trails

Type your goal or command to begin...
`;

    console.log(welcome);
  }

  showHelp(): void {
    const help = `
${this.colorize('Available Commands:', 'cyan')}

${this.colorize('Agent Control:', 'yellow')}
  ${this.colorize('/help', 'green')}           Show this help message
  ${this.colorize('/status', 'green')}         Show agent status and configuration
  ${this.colorize('/stop', 'green')}           Stop current operation
  ${this.colorize('/exit', 'green')}           Exit the CLI

${this.colorize('Information:', 'yellow')}
  ${this.colorize('/context', 'green')}        Show current context information
  ${this.colorize('/providers', 'green')}      List LLM providers and their status
  ${this.colorize('/tools', 'green')}          List available tools
  ${this.colorize('/config', 'green')}         Show current configuration
  ${this.colorize('/history', 'green')}        Show recent action history

${this.colorize('Session Management:', 'yellow')}
  ${this.colorize('/save [name]', 'green')}    Save current session
  ${this.colorize('/load <name>', 'green')}    Load a saved session
  ${this.colorize('/clear', 'green')}          Clear terminal screen

${this.colorize('Goal Examples:', 'blue')}
• "Create a new React component with TypeScript"
• "Analyze code quality and suggest improvements"
• "Find and fix ESLint errors in all JavaScript files"
• "Generate API documentation from TypeScript interfaces"
• "Set up CI/CD pipeline configuration"
• "Optimize package.json dependencies"

${this.colorize('Advanced Features:', 'magenta')}
• The agent can chain multiple operations automatically
• Parallel execution for independent tasks
• Intelligent error recovery and retry mechanisms
• Context-aware suggestions based on project structure
• Cross-platform command execution (Windows/Unix)

${this.colorize('Safety Notes:', 'red')}
• Dangerous operations require explicit confirmation
• File backups are created before destructive operations
• All actions are logged for audit purposes
• Use ${this.colorize('/stop', 'green')} to interrupt long-running operations
`;

    console.log(help);
  }

  showGoodbye(): void {
    const goodbye = `
${this.colorize('Thank you for using the Autonomous Agent CLI!', 'cyan')}

${this.colorize('Session Summary:', 'yellow')}
• Your session has been automatically saved
• Context and history are preserved for next time
• Check the logs for detailed operation records

${this.colorize('Feedback & Support:', 'blue')}
• Report issues: https://github.com/yourusername/agentic-cli-tool/issues
• Documentation: https://github.com/yourusername/agentic-cli-tool#readme
• Community: https://discord.gg/your-discord-link

${this.colorize('Until next time! 🚀', 'green')}
`;

    console.log(goodbye);
  }

  showError(message: string, details?: any): void {
    console.log(this.colorize('❌ Error:', 'red'), message);
    if (details && this.configManager.getUIConfig().verbosity !== 'silent') {
      console.log(this.colorize('Details:', 'gray'), details);
    }
  }

  showWarning(message: string): void {
    console.log(this.colorize('⚠️  Warning:', 'yellow'), message);
  }

  showSuccess(message: string): void {
    console.log(this.colorize('✅ Success:', 'green'), message);
  }

  showInfo(message: string): void {
    console.log(this.colorize('ℹ️  Info:', 'blue'), message);
  }

  showProgress(message: string): void {
    console.log(this.colorize('⏳ Progress:', 'cyan'), message);
  }

  showTaskStart(taskName: string): void {
    console.log(this.colorize('🔄 Starting:', 'cyan'), taskName);
  }

  showTaskComplete(taskName: string, duration: number): void {
    console.log(this.colorize('✅ Completed:', 'green'), `${taskName} (${duration}ms)`);
  }

  showTaskFailed(taskName: string, error: string): void {
    console.log(this.colorize('❌ Failed:', 'red'), `${taskName} - ${error}`);
  }

  showPlanCreated(taskCount: number, estimatedDuration: number): void {
    const minutes = Math.ceil(estimatedDuration / 60);
    console.log(
      this.colorize('📋 Plan Created:', 'blue'),
      `${taskCount} tasks, estimated ${minutes} minute(s)`
    );
  }

  showProviderSwitch(from: string, to: string): void {
    console.log(
      this.colorize('🔄 Provider Switch:', 'yellow'),
      `${from} → ${to}`
    );
  }

  showFileOperation(operation: string, path: string): void {
    console.log(
      this.colorize('📁 File Operation:', 'blue'),
      `${operation}: ${path}`
    );
  }

  showShellCommand(command: string, workingDir: string): void {
    console.log(
      this.colorize('💻 Shell Command:', 'magenta'),
      `${command} (in ${workingDir})`
    );
  }

  showContextUpdate(type: string, details: string): void {
    console.log(
      this.colorize('🔄 Context Update:', 'cyan'),
      `${type}: ${details}`
    );
  }

  showTable(headers: string[], rows: string[][]): void {
    if (!this.colors) {
      // Simple text table for no-color mode
      console.log(headers.join('\t'));
      console.log('-'.repeat(headers.join('\t').length));
      rows.forEach(row => console.log(row.join('\t')));
      return;
    }

    // Calculate column widths
    const widths = headers.map((header, i) => 
      Math.max(header.length, ...rows.map(row => (row[i] || '').length))
    );

    // Print header
    const headerRow = headers.map((header, i) => 
      this.colorize(header.padEnd(widths[i]!), 'cyan')
    ).join(' │ ');
    console.log('┌─' + widths.map(w => '─'.repeat(w)).join('─┬─') + '─┐');
    console.log('│ ' + headerRow + ' │');
    console.log('├─' + widths.map(w => '─'.repeat(w)).join('─┼─') + '─┤');

    // Print rows
    rows.forEach(row => {
      const formattedRow = row.map((cell, i) => 
        (cell || '').padEnd(widths[i]!)
      ).join(' │ ');
      console.log('│ ' + formattedRow + ' │');
    });

    console.log('└─' + widths.map(w => '─'.repeat(w)).join('─┴─') + '─┘');
  }

  showProgressBar(current: number, total: number, label: string): void {
    const percentage = Math.round((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.round((barLength * current) / total);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    
    const progressText = `${label} [${bar}] ${percentage}% (${current}/${total})`;
    
    // Clear line and print progress
    process.stdout.write('\r' + ' '.repeat(80) + '\r');
    process.stdout.write(this.colorize(progressText, 'cyan'));
    
    if (current === total) {
      console.log(); // New line when complete
    }
  }

  showSpinner(message: string, id?: string): () => void {
    // This is a simple text-based spinner
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let i = 0;

    const interval = setInterval(() => {
      process.stdout.write('\r' + this.colorize(frames[i % frames.length]!, 'cyan') + ' ' + message);
      i++;
    }, 100);

    if (id) {
      this.activeSpinners.set(id, interval);
    }

    return () => {
      clearInterval(interval);
      if (id) {
        this.activeSpinners.delete(id);
      }
      process.stdout.write('\r' + ' '.repeat(message.length + 2) + '\r');
    };
  }

  formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  }

  formatTimestamp(date: Date): string {
    return date.toLocaleString();
  }

  private colorize(text: string, color: string): string {
    if (!this.colors) return text;
    
    switch (color) {
      case 'red': return chalk.red(text);
      case 'green': return chalk.green(text);
      case 'yellow': return chalk.yellow(text);
      case 'blue': return chalk.blue(text);
      case 'magenta': return chalk.magenta(text);
      case 'cyan': return chalk.cyan(text);
      case 'gray': return chalk.gray(text);
      case 'white': return chalk.white(text);
      case 'bold': return chalk.bold(text);
      case 'dim': return chalk.dim(text);
      default: return text;
    }
  }

  setColors(enabled: boolean): void {
    // Update the colors setting
    Object.defineProperty(this, 'colors', { value: enabled, writable: true });
  }

  clearScreen(): void {
    console.clear();
  }

  moveCursor(x: number, y: number): void {
    process.stdout.write(`\x1b[${y};${x}H`);
  }

  hideCursor(): void {
    process.stdout.write('\x1b[?25l');
  }

  showCursor(): void {
    process.stdout.write('\x1b[?25h');
  }

  // Real-time streaming methods
  startStreaming(): void {
    this.streamingOutput = true;
    this.emit('streamingStarted');
  }

  stopStreaming(): void {
    this.streamingOutput = false;
    this.emit('streamingStopped');
  }

  streamTaskUpdate(task: AgentTask): void {
    if (!this.streamingOutput) return;

    const status = this.getTaskStatusIcon(task.status);
    const duration = task.updatedAt.getTime() - task.createdAt.getTime();

    console.log(
      `${status} ${this.colorize(task.description, 'white')} ${this.colorize(`(${this.formatDuration(duration)})`, 'gray')}`
    );
  }

  streamToolExecution(toolName: string, operation: string): void {
    if (!this.streamingOutput) return;

    console.log(
      this.colorize('🔧 Tool:', 'blue'),
      `${toolName} - ${operation}`
    );
  }

  streamLLMResponse(provider: string, tokens: number): void {
    if (!this.streamingOutput) return;

    console.log(
      this.colorize('🧠 LLM:', 'magenta'),
      `${provider} (${tokens} tokens)`
    );
  }

  streamPlanProgress(plan: AgentPlan, completedTasks: number): void {
    if (!this.streamingOutput) return;

    this.showProgressBar(completedTasks, plan.tasks.length, 'Plan Progress');
  }

  showTaskList(tasks: AgentTask[]): void {
    console.log(this.colorize('\n📋 Task List:', 'cyan'));

    tasks.forEach((task, index) => {
      const status = this.getTaskStatusIcon(task.status);
      const priority = this.getPriorityIcon(task.priority);

      console.log(
        `${index + 1}. ${status} ${priority} ${task.description}`
      );

      if (task.dependencies.length > 0) {
        console.log(
          `   ${this.colorize('Dependencies:', 'gray')} ${task.dependencies.join(', ')}`
        );
      }
    });
    console.log();
  }

  showPlanSummary(plan: AgentPlan): void {
    console.log(this.colorize('\n📊 Plan Summary:', 'cyan'));
    console.log(`Goal: ${this.colorize(plan.goal, 'white')}`);
    console.log(`Tasks: ${this.colorize(plan.tasks.length.toString(), 'yellow')}`);
    console.log(`Parallelizable: ${this.colorize(plan.parallelizable ? 'Yes' : 'No', plan.parallelizable ? 'green' : 'red')}`);
    console.log(`Estimated Duration: ${this.colorize(this.formatDuration(plan.estimatedDuration * 1000), 'blue')}`);
    console.log();
  }

  showToolResult(toolName: string, result: ToolResult): void {
    const status = result.success ? '✅' : '❌';
    const color = result.success ? 'green' : 'red';

    console.log(
      `${status} ${this.colorize('Tool Result:', color)} ${toolName}`
    );

    if (result.success && result.data) {
      this.showResultData(result.data);
    } else if (!result.success && result.error) {
      console.log(this.colorize(`   Error: ${result.error}`, 'red'));
    }
  }

  showResultData(data: unknown): void {
    if (typeof data === 'object' && data !== null) {
      const formatted = JSON.stringify(data, null, 2);
      const lines = formatted.split('\n');

      if (lines.length > 10) {
        // Show first few lines and indicate truncation
        console.log(this.colorize('   Result:', 'gray'));
        lines.slice(0, 8).forEach(line => {
          console.log(`   ${this.colorize(line, 'white')}`);
        });
        console.log(this.colorize(`   ... (${lines.length - 8} more lines)`, 'gray'));
      } else {
        console.log(this.colorize('   Result:', 'gray'));
        lines.forEach(line => {
          console.log(`   ${this.colorize(line, 'white')}`);
        });
      }
    } else {
      console.log(this.colorize(`   Result: ${String(data)}`, 'white'));
    }
  }

  private getTaskStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return this.colorize('⏳', 'yellow');
      case 'running': return this.colorize('🔄', 'cyan');
      case 'completed': return this.colorize('✅', 'green');
      case 'failed': return this.colorize('❌', 'red');
      default: return this.colorize('❓', 'gray');
    }
  }

  private getPriorityIcon(priority: number): string {
    if (priority >= 8) return this.colorize('🔴', 'red');
    if (priority >= 6) return this.colorize('🟡', 'yellow');
    if (priority >= 4) return this.colorize('🟢', 'green');
    return this.colorize('⚪', 'gray');
  }

  stopAllSpinners(): void {
    for (const [, interval] of this.activeSpinners.entries()) {
      clearInterval(interval);
    }
    this.activeSpinners.clear();
    process.stdout.write('\r' + ' '.repeat(80) + '\r');
  }

  shutdown(): void {
    this.stopAllSpinners();
    this.stopStreaming();
    this.removeAllListeners();
  }
}
