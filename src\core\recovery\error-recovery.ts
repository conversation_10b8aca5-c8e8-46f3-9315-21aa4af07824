import { EventEmitter } from 'events';
import { <PERSON><PERSON> } from 'winston';
import { <PERSON><PERSON><PERSON>, Agent<PERSON>ontext, ToolR<PERSON>ult } from '@/types';

export interface RecoveryStrategy {
  name: string;
  description: string;
  canHandle: (error: Error, task: AgentTask, context: AgentContext) => boolean;
  execute: (error: Error, task: AgentTask, context: AgentContext) => Promise<ToolResult>;
  priority: number;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
}

export class ErrorRecoveryManager extends EventEmitter {
  private readonly logger: Logger;
  private strategies: Map<string, RecoveryStrategy> = new Map();
  private retryHistory: Map<string, number> = new Map();

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.initializeDefaultStrategies();
  }

  private initializeDefaultStrategies(): void {
    const strategies: RecoveryStrategy[] = [
      {
        name: 'file_not_found_recovery',
        description: 'Attempts to recover from file not found errors',
        priority: 1,
        canHandle: (error, task) => {
          return error.message.toLowerCase().includes('file not found') ||
                 error.message.toLowerCase().includes('enoent') ||
                 (task.type === 'filesystem' && error.message.includes('no such file'));
        },
        execute: async (_error, task, _context) => {
          this.logger.info('Attempting file not found recovery', { taskId: task.id });
          
          // Try to create parent directories if it's a write operation
          if (task.description.toLowerCase().includes('write') || 
              task.description.toLowerCase().includes('create')) {
            return {
              success: false,
              error: 'File not found - suggest creating parent directories first',
              metadata: {
                recoveryStrategy: 'file_not_found_recovery',
                suggestion: 'Create parent directories before writing file',
              },
            };
          }
          
          // For read operations, suggest alternative files
          return {
            success: false,
            error: 'File not found - no recovery possible for read operation',
            metadata: {
              recoveryStrategy: 'file_not_found_recovery',
              suggestion: 'Check file path or use search tool to find similar files',
            },
          };
        },
      },
      {
        name: 'permission_denied_recovery',
        description: 'Attempts to recover from permission denied errors',
        priority: 2,
        canHandle: (error) => {
          return error.message.toLowerCase().includes('permission denied') ||
                 error.message.toLowerCase().includes('eacces') ||
                 error.message.toLowerCase().includes('eperm');
        },
        execute: async (_error, task, _context) => {
          this.logger.info('Attempting permission recovery', { taskId: task.id });
          
          return {
            success: false,
            error: 'Permission denied - consider using elevated privileges',
            metadata: {
              recoveryStrategy: 'permission_denied_recovery',
              suggestion: 'Run with elevated privileges or change file permissions',
            },
          };
        },
      },
      {
        name: 'network_timeout_recovery',
        description: 'Attempts to recover from network timeout errors',
        priority: 3,
        canHandle: (error) => {
          return error.message.toLowerCase().includes('timeout') ||
                 error.message.toLowerCase().includes('etimedout') ||
                 error.message.toLowerCase().includes('connection refused');
        },
        execute: async (_error, task, _context) => {
          this.logger.info('Attempting network timeout recovery', { taskId: task.id });
          
          // Suggest retry with exponential backoff
          return {
            success: false,
            error: 'Network timeout - retry recommended',
            metadata: {
              recoveryStrategy: 'network_timeout_recovery',
              suggestion: 'Retry operation with exponential backoff',
              retryable: true,
            },
          };
        },
      },
      {
        name: 'syntax_error_recovery',
        description: 'Attempts to recover from syntax errors in commands',
        priority: 4,
        canHandle: (error, task) => {
          return (task.type === 'shell' && 
                  (error.message.toLowerCase().includes('syntax error') ||
                   error.message.toLowerCase().includes('command not found') ||
                   error.message.toLowerCase().includes('invalid option')));
        },
        execute: async (_error, task, _context) => {
          this.logger.info('Attempting syntax error recovery', { taskId: task.id });
          
          return {
            success: false,
            error: 'Syntax error in command - suggest command correction',
            metadata: {
              recoveryStrategy: 'syntax_error_recovery',
              suggestion: 'Check command syntax and available options',
              retryable: true,
            },
          };
        },
      },
    ];

    strategies.forEach(strategy => {
      this.strategies.set(strategy.name, strategy);
    });

    this.logger.info(`Initialized ${strategies.length} recovery strategies`);
  }

  async attemptRecovery(
    error: Error,
    task: AgentTask,
    context: AgentContext
  ): Promise<ToolResult | null> {
    this.logger.info('Attempting error recovery', { 
      taskId: task.id, 
      error: error.message 
    });

    // Find applicable strategies
    const applicableStrategies = Array.from(this.strategies.values())
      .filter(strategy => strategy.canHandle(error, task, context))
      .sort((a, b) => a.priority - b.priority);

    if (applicableStrategies.length === 0) {
      this.logger.warn('No recovery strategies available', { 
        taskId: task.id, 
        error: error.message 
      });
      return null;
    }

    // Try each strategy
    for (const strategy of applicableStrategies) {
      try {
        this.logger.info('Executing recovery strategy', { 
          strategy: strategy.name, 
          taskId: task.id 
        });

        const result = await strategy.execute(error, task, context);
        
        this.emit('recoveryAttempted', {
          strategy: strategy.name,
          task,
          error,
          result,
        });

        if (result.success) {
          this.logger.info('Recovery successful', { 
            strategy: strategy.name, 
            taskId: task.id 
          });
          return result;
        } else if (result.metadata?.['retryable']) {
          this.logger.info('Recovery suggests retry', { 
            strategy: strategy.name, 
            taskId: task.id 
          });
          return result;
        }
      } catch (recoveryError) {
        this.logger.warn('Recovery strategy failed', { 
          strategy: strategy.name, 
          taskId: task.id, 
          recoveryError 
        });
      }
    }

    this.logger.warn('All recovery strategies failed', { taskId: task.id });
    return null;
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    context?: { taskId?: string; description?: string }
  ): Promise<T> {
    const retryConfig: RetryConfig = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      jitter: true,
      ...config,
    };

    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        const result = await operation();
        
        if (attempt > 1) {
          this.logger.info('Operation succeeded after retry', { 
            attempt, 
            context 
          });
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        
        this.logger.warn('Operation failed', { 
          attempt, 
          maxAttempts: retryConfig.maxAttempts,
          error: lastError.message,
          context 
        });

        if (attempt === retryConfig.maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff and optional jitter
        let delay = Math.min(
          retryConfig.baseDelay * Math.pow(retryConfig.backoffMultiplier, attempt - 1),
          retryConfig.maxDelay
        );

        if (retryConfig.jitter) {
          delay = delay * (0.5 + Math.random() * 0.5);
        }

        this.logger.info('Retrying operation', { 
          attempt: attempt + 1, 
          delay, 
          context 
        });

        await this.sleep(delay);
      }
    }

    this.emit('retryExhausted', {
      maxAttempts: retryConfig.maxAttempts,
      lastError,
      context,
    });

    throw lastError || new Error('Operation failed after all retry attempts');
  }

  registerStrategy(strategy: RecoveryStrategy): void {
    this.strategies.set(strategy.name, strategy);
    this.logger.info('Recovery strategy registered', { name: strategy.name });
  }

  removeStrategy(name: string): void {
    this.strategies.delete(name);
    this.logger.info('Recovery strategy removed', { name });
  }

  getStrategies(): RecoveryStrategy[] {
    return Array.from(this.strategies.values());
  }

  getRetryHistory(): Map<string, number> {
    return new Map(this.retryHistory);
  }

  clearRetryHistory(): void {
    this.retryHistory.clear();
    this.logger.info('Retry history cleared');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async analyzeFailurePatterns(): Promise<{
    commonErrors: Array<{ error: string; count: number }>;
    recoverySuccessRate: number;
    mostEffectiveStrategies: Array<{ strategy: string; successRate: number }>;
  }> {
    // This would be enhanced with actual failure tracking
    // For now, return basic structure
    return {
      commonErrors: [],
      recoverySuccessRate: 0,
      mostEffectiveStrategies: [],
    };
  }

  async shutdown(): Promise<void> {
    this.strategies.clear();
    this.retryHistory.clear();
    this.removeAllListeners();
    this.logger.info('Error recovery manager shutdown');
  }
}
