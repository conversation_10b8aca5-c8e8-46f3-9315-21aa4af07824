import { EventEmitter } from 'events';
import { Logger } from 'winston';
import PQueue from 'p-queue';
import pLimit from 'p-limit';

export interface ParallelTask<T = any> {
  id: string;
  name: string;
  execute: () => Promise<T>;
  priority?: number;
  timeout?: number;
  retries?: number;
}

export interface ParallelResult<T = any> {
  id: string;
  success: boolean;
  result?: T;
  error?: Error;
  duration: number;
  retryCount: number;
}

export class ParallelExecutor extends EventEmitter {
  private readonly logger: Logger;
  private readonly queue: PQueue;
  private activeTasks = new Map<string, Promise<any>>();

  constructor(logger: Logger, maxConcurrency = 5) {
    super();
    this.logger = logger;
    this.queue = new PQueue({ 
      concurrency: maxConcurrency,
      timeout: 60000, // 1 minute default timeout
      throwOnTimeout: true,
    });
  }

  async executeParallel<T>(
    tasks: Array<() => Promise<T>>,
    maxConcurrency?: number
  ): Promise<T[]> {
    if (tasks.length === 0) {
      return [];
    }

    this.logger.info(`Executing ${tasks.length} tasks in parallel`, { 
      maxConcurrency: maxConcurrency || this.queue.concurrency 
    });

    const limit = pLimit(maxConcurrency || this.queue.concurrency);
    const startTime = Date.now();

    try {
      const results = await Promise.all(
        tasks.map((task, index) =>
          limit(async () => {
            const taskStartTime = Date.now();
            this.emit('taskStarted', { index, taskCount: tasks.length });

            try {
              const result = await task();
              const duration = Date.now() - taskStartTime;
              
              this.emit('taskCompleted', { 
                index, 
                duration, 
                success: true 
              });
              
              return result;
            } catch (error) {
              const duration = Date.now() - taskStartTime;
              
              this.emit('taskFailed', { 
                index, 
                duration, 
                error 
              });
              
              throw error;
            }
          })
        )
      );

      const totalDuration = Date.now() - startTime;
      this.logger.info(`Parallel execution completed`, { 
        taskCount: tasks.length,
        duration: totalDuration,
        successCount: results.length 
      });

      return results;
    } catch (error) {
      const totalDuration = Date.now() - startTime;
      this.logger.error('Parallel execution failed', { 
        error, 
        duration: totalDuration 
      });
      throw error;
    }
  }

  async executeWithRetry<T>(
    tasks: ParallelTask<T>[],
    options: {
      maxConcurrency?: number;
      defaultRetries?: number;
      defaultTimeout?: number;
    } = {}
  ): Promise<ParallelResult<T>[]> {
    const {
      maxConcurrency = this.queue.concurrency,
      defaultRetries = 3,
      defaultTimeout = 30000,
    } = options;

    this.logger.info(`Executing ${tasks.length} tasks with retry logic`, {
      maxConcurrency,
      defaultRetries,
      defaultTimeout,
    });

    const limit = pLimit(maxConcurrency);
    const results: ParallelResult<T>[] = [];

    const taskPromises = tasks.map(task =>
      limit(async () => {
        const result = await this.executeTaskWithRetry(
          task,
          task.retries ?? defaultRetries,
          task.timeout ?? defaultTimeout
        );
        results.push(result);
        return result;
      })
    );

    await Promise.allSettled(taskPromises);
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    this.logger.info('Parallel execution with retry completed', {
      total: results.length,
      success: successCount,
      failed: failureCount,
    });

    return results;
  }

  private async executeTaskWithRetry<T>(
    task: ParallelTask<T>,
    maxRetries: number,
    timeout: number
  ): Promise<ParallelResult<T>> {
    let lastError: Error | undefined;
    let retryCount = 0;
    const startTime = Date.now();

    this.emit('taskStarted', { taskId: task.id, name: task.name });

    while (retryCount <= maxRetries) {
      try {
        const result = await this.executeWithTimeout(task.execute, timeout);
        const duration = Date.now() - startTime;

        this.emit('taskCompleted', {
          taskId: task.id,
          name: task.name,
          duration,
          retryCount,
        });

        return {
          id: task.id,
          success: true,
          result,
          duration,
          retryCount,
        };
      } catch (error) {
        lastError = error as Error;
        retryCount++;

        this.logger.warn(`Task ${task.name} failed, attempt ${retryCount}/${maxRetries + 1}`, {
          taskId: task.id,
          error: lastError.message,
        });

        this.emit('taskRetry', {
          taskId: task.id,
          name: task.name,
          retryCount,
          maxRetries,
          error: lastError,
        });

        if (retryCount <= maxRetries) {
          // Exponential backoff
          const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 10000);
          await this.sleep(delay);
        }
      }
    }

    const duration = Date.now() - startTime;

    this.emit('taskFailed', {
      taskId: task.id,
      name: task.name,
      duration,
      retryCount,
      error: lastError,
    });

    return {
      id: task.id,
      success: false,
      error: lastError || new Error('Task failed'),
      duration,
      retryCount,
    };
  }

  private async executeWithTimeout<T>(
    task: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Task timed out after ${timeout}ms`));
      }, timeout);

      task()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  async executeBatch<T>(
    tasks: Array<() => Promise<T>>,
    batchSize: number,
    delayBetweenBatches = 0
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < tasks.length; i += batchSize) {
      const batch = tasks.slice(i, i + batchSize);
      
      this.logger.info(`Executing batch ${Math.floor(i / batchSize) + 1}`, {
        batchSize: batch.length,
        totalBatches: Math.ceil(tasks.length / batchSize),
      });

      const batchResults = await this.executeParallel(batch);
      results.push(...batchResults);

      // Delay between batches if specified
      if (delayBetweenBatches > 0 && i + batchSize < tasks.length) {
        await this.sleep(delayBetweenBatches);
      }
    }

    return results;
  }

  async executeConditional<T>(
    tasks: Array<{
      task: () => Promise<T>;
      condition: (previousResults: T[]) => boolean;
      name?: string;
    }>
  ): Promise<T[]> {
    const results: T[] = [];

    for (const { task, condition, name } of tasks) {
      if (condition(results)) {
        this.logger.info(`Executing conditional task: ${name || 'unnamed'}`);
        
        try {
          const result = await task();
          results.push(result);
        } catch (error) {
          this.logger.error(`Conditional task failed: ${name || 'unnamed'}`, { error });
          throw error;
        }
      } else {
        this.logger.info(`Skipping conditional task: ${name || 'unnamed'}`);
      }
    }

    return results;
  }

  getQueueStatus(): {
    pending: number;
    running: number;
    completed: number;
    failed: number;
  } {
    return {
      pending: this.queue.pending,
      running: this.queue.pending,
      completed: 0, // PQueue doesn't track this
      failed: 0, // PQueue doesn't track this
    };
  }

  async waitForAll(): Promise<void> {
    await this.queue.onIdle();
  }

  async clear(): Promise<void> {
    this.queue.clear();
    this.activeTasks.clear();
  }

  async shutdown(): Promise<void> {
    await this.clear();
    this.removeAllListeners();
    this.logger.info('Parallel executor shutdown');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility method for creating tasks
  createTask<T>(
    id: string,
    name: string,
    execute: () => Promise<T>,
    options: {
      priority?: number;
      timeout?: number;
      retries?: number;
    } = {}
  ): ParallelTask<T> {
    return {
      id,
      name,
      execute,
      ...options,
    };
  }

  // Method to execute tasks with different priorities
  async executePrioritized<T>(
    tasks: ParallelTask<T>[],
    maxConcurrency?: number
  ): Promise<ParallelResult<T>[]> {
    // Sort tasks by priority (higher priority first)
    const sortedTasks = [...tasks].sort((a, b) => (b.priority || 0) - (a.priority || 0));
    
    return this.executeWithRetry(sortedTasks, { maxConcurrency: maxConcurrency || this.queue.concurrency });
  }
}
