import { createLogger, format, transports, Logger } from 'winston';
import path from 'path';
import { promises as fs } from 'fs';

export interface LoggerConfig {
  level: string;
  logDir: string;
  maxFiles: number;
  maxSize: string;
  enableConsole: boolean;
  enableFile: boolean;
  format: 'json' | 'simple' | 'detailed';
}

const defaultConfig: LoggerConfig = {
  level: 'info',
  logDir: './logs',
  maxFiles: 5,
  maxSize: '10m',
  enableConsole: true,
  enableFile: true,
  format: 'detailed',
};

export async function createAgentLogger(config: Partial<LoggerConfig> = {}): Promise<Logger> {
  const finalConfig = { ...defaultConfig, ...config };

  // Ensure log directory exists
  if (finalConfig.enableFile) {
    await fs.mkdir(finalConfig.logDir, { recursive: true });
  }

  const loggerTransports: any[] = [];

  // Console transport
  if (finalConfig.enableConsole) {
    loggerTransports.push(
      new transports.Console({
        level: finalConfig.level,
        format: format.combine(
          format.colorize(),
          format.timestamp({ format: 'HH:mm:ss' }),
          format.printf(({ timestamp, level, message, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${level}] ${message} ${metaStr}`;
          })
        ),
      })
    );
  }

  // File transports
  if (finalConfig.enableFile) {
    // General log file
    loggerTransports.push(
      new transports.File({
        filename: path.join(finalConfig.logDir, 'agent.log'),
        level: finalConfig.level,
        maxFiles: finalConfig.maxFiles,
        maxsize: parseSize(finalConfig.maxSize),
        format: getFileFormat(finalConfig.format),
      })
    );

    // Error log file
    loggerTransports.push(
      new transports.File({
        filename: path.join(finalConfig.logDir, 'error.log'),
        level: 'error',
        maxFiles: finalConfig.maxFiles,
        maxsize: parseSize(finalConfig.maxSize),
        format: getFileFormat(finalConfig.format),
      })
    );

    // Debug log file (if debug level is enabled)
    if (finalConfig.level === 'debug') {
      loggerTransports.push(
        new transports.File({
          filename: path.join(finalConfig.logDir, 'debug.log'),
          level: 'debug',
          maxFiles: finalConfig.maxFiles,
          maxsize: parseSize(finalConfig.maxSize),
          format: getFileFormat(finalConfig.format),
        })
      );
    }
  }

  const logger = createLogger({
    level: finalConfig.level,
    format: format.combine(
      format.timestamp(),
      format.errors({ stack: true }),
      format.metadata({ fillExcept: ['message', 'level', 'timestamp'] })
    ),
    transports: loggerTransports,
    exitOnError: false,
  });

  // Add custom methods
  addCustomMethods(logger);

  return logger;
}

function getFileFormat(formatType: string) {
  switch (formatType) {
    case 'json':
      return format.combine(
        format.timestamp(),
        format.errors({ stack: true }),
        format.json()
      );
    
    case 'simple':
      return format.combine(
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        format.printf(({ timestamp, level, message }) => {
          return `${timestamp} [${level.toUpperCase()}] ${message}`;
        })
      );
    
    case 'detailed':
    default:
      return format.combine(
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        format.errors({ stack: true }),
        format.printf(({ timestamp, level, message, metadata, stack }) => {
          let log = `${timestamp} [${level.toUpperCase()}] ${message}`;
          
          if (metadata && Object.keys(metadata).length > 0) {
            log += `\n  Metadata: ${JSON.stringify(metadata, null, 2)}`;
          }
          
          if (stack) {
            log += `\n  Stack: ${stack}`;
          }
          
          return log;
        })
      );
  }
}

function parseSize(sizeStr: string): number {
  const match = sizeStr.match(/^(\d+)([kmg]?)$/i);
  if (!match) return 10 * 1024 * 1024; // Default 10MB

  const size = parseInt(match[1]!, 10);
  const unit = (match[2] || '').toLowerCase();

  switch (unit) {
    case 'k':
      return size * 1024;
    case 'm':
      return size * 1024 * 1024;
    case 'g':
      return size * 1024 * 1024 * 1024;
    default:
      return size;
  }
}

function addCustomMethods(logger: Logger): void {
  // Add agent-specific logging methods
  (logger as any).agent = (message: string, meta?: any) => {
    logger.info(`[AGENT] ${message}`, meta);
  };

  (logger as any).tool = (message: string, meta?: any) => {
    logger.info(`[TOOL] ${message}`, meta);
  };

  (logger as any).provider = (message: string, meta?: any) => {
    logger.info(`[PROVIDER] ${message}`, meta);
  };

  (logger as any).context = (message: string, meta?: any) => {
    logger.debug(`[CONTEXT] ${message}`, meta);
  };

  (logger as any).session = (message: string, meta?: any) => {
    logger.info(`[SESSION] ${message}`, meta);
  };

  (logger as any).security = (message: string, meta?: any) => {
    logger.warn(`[SECURITY] ${message}`, meta);
  };

  (logger as any).performance = (message: string, meta?: any) => {
    logger.info(`[PERFORMANCE] ${message}`, meta);
  };

  (logger as any).audit = (message: string, meta?: any) => {
    logger.info(`[AUDIT] ${message}`, meta);
  };
}

// Performance logging utilities
export class PerformanceLogger {
  private timers = new Map<string, number>();
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  start(operation: string): void {
    this.timers.set(operation, Date.now());
    (this.logger as any).performance(`Started: ${operation}`);
  }

  end(operation: string, metadata?: any): number {
    const startTime = this.timers.get(operation);
    if (!startTime) {
      this.logger.warn(`No start time found for operation: ${operation}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(operation);

    (this.logger as any).performance(`Completed: ${operation}`, {
      duration,
      ...metadata,
    });

    return duration;
  }

  measure<T>(operation: string, fn: () => Promise<T>): Promise<T>;
  measure<T>(operation: string, fn: () => T): T;
  measure<T>(operation: string, fn: () => T | Promise<T>): T | Promise<T> {
    this.start(operation);
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => this.end(operation));
      } else {
        this.end(operation);
        return result;
      }
    } catch (error) {
      this.end(operation, { error: (error as Error).message });
      throw error;
    }
  }
}

// Audit logging utilities
export class AuditLogger {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  logAction(action: string, details: any): void {
    (this.logger as any).audit(`Action: ${action}`, {
      timestamp: new Date().toISOString(),
      action,
      details,
    });
  }

  logFileOperation(operation: string, path: string, metadata?: any): void {
    this.logAction('file_operation', {
      operation,
      path,
      ...metadata,
    });
  }

  logShellCommand(command: string, workingDir: string, metadata?: any): void {
    this.logAction('shell_command', {
      command,
      workingDir,
      ...metadata,
    });
  }

  logProviderCall(provider: string, model: string, metadata?: any): void {
    this.logAction('provider_call', {
      provider,
      model,
      ...metadata,
    });
  }

  logSecurityEvent(event: string, details: any): void {
    (this.logger as any).security(`Security event: ${event}`, {
      timestamp: new Date().toISOString(),
      event,
      details,
    });
  }
}

// Error logging utilities
export function logError(logger: Logger, error: Error, context?: any): void {
  logger.error(error.message, {
    name: error.name,
    stack: error.stack,
    context,
  });
}

export function logWarning(logger: Logger, message: string, context?: any): void {
  logger.warn(message, context);
}

// Log rotation utilities
export async function rotateLogs(logDir: string, maxAge: number = 7): Promise<void> {
  try {
    const files = await fs.readdir(logDir);
    const cutoffTime = Date.now() - maxAge * 24 * 60 * 60 * 1000;

    for (const file of files) {
      const filePath = path.join(logDir, file);
      const stats = await fs.stat(filePath);
      
      if (stats.mtime.getTime() < cutoffTime) {
        await fs.unlink(filePath);
      }
    }
  } catch (error) {
    // Ignore rotation errors
  }
}

// Log analysis utilities
export async function analyzeLogs(logDir: string): Promise<{
  totalLines: number;
  errorCount: number;
  warningCount: number;
  topErrors: Array<{ message: string; count: number }>;
}> {
  try {
    const logFile = path.join(logDir, 'agent.log');
    const content = await fs.readFile(logFile, 'utf8');
    const lines = content.split('\n').filter(Boolean);

    const errorLines = lines.filter(line => line.includes('[ERROR]'));
    const warningLines = lines.filter(line => line.includes('[WARN]'));

    // Count error messages
    const errorCounts = new Map<string, number>();
    errorLines.forEach(line => {
      const match = line.match(/\[ERROR\]\s+(.+?)(?:\s+{|$)/);
      if (match) {
        const message = match[1]!;
        errorCounts.set(message, (errorCounts.get(message) || 0) + 1);
      }
    });

    const topErrors = Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    return {
      totalLines: lines.length,
      errorCount: errorLines.length,
      warningCount: warningLines.length,
      topErrors,
    };
  } catch (error) {
    return {
      totalLines: 0,
      errorCount: 0,
      warningCount: 0,
      topErrors: [],
    };
  }
}
