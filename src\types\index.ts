import { z } from 'zod';

// Core Agent Types
export interface AgentTask {
  id: string;
  type: 'filesystem' | 'shell' | 'analysis' | 'planning' | 'execution';
  description: string;
  priority: number;
  dependencies: string[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  result?: unknown;
  error?: Error;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentPlan {
  id: string;
  goal: string;
  tasks: AgentTask[];
  parallelizable: boolean;
  estimatedDuration: number;
  createdAt: Date;
}

export interface AgentContext {
  sessionId: string;
  workingDirectory: string;
  projectStructure: ProjectStructure;
  environment: EnvironmentInfo;
  history: AgentAction[];
  memory: ContextMemory;
}

export interface ProjectStructure {
  root: string;
  files: FileInfo[];
  directories: DirectoryInfo[];
  gitInfo?: GitInfo | undefined;
  packageInfo?: PackageInfo | undefined;
}

export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  size: number;
  lastModified: Date;
  type: 'file' | 'directory' | 'symlink';
  permissions: string;
  content?: string;
}

export interface DirectoryInfo {
  path: string;
  name: string;
  children: string[];
  size: number;
  lastModified: Date;
}

export interface GitInfo {
  branch: string;
  commit: string;
  remote?: string | undefined;
  status: string[];
}

export interface PackageInfo {
  name: string;
  version: string;
  type: 'npm' | 'python' | 'rust' | 'go' | 'other';
  dependencies: Record<string, string>;
  scripts: Record<string, string>;
}

export interface EnvironmentInfo {
  platform: NodeJS.Platform;
  arch: string;
  nodeVersion: string;
  npmVersion?: string | undefined;
  pythonVersion?: string | undefined;
  gitVersion?: string | undefined;
  shell: string;
  cwd: string;
  env: Record<string, string>;
}

export interface AgentAction {
  id: string;
  type: string;
  input: unknown;
  output: unknown;
  timestamp: Date;
  duration: number;
  success: boolean;
  error?: string | undefined;
}

export interface ContextMemory {
  facts: Record<string, unknown>;
  patterns: string[];
  preferences: Record<string, unknown>;
  learnings: string[];
}

// LLM Provider Types
export interface LLMProvider {
  name: string;
  type: 'openai' | 'anthropic' | 'google' | 'mistral' | 'deepseek' | 'ollama';
  apiKey?: string | undefined;
  baseUrl?: string | undefined;
  model: string;
  maxTokens: number;
  temperature: number;
  priority?: number | undefined;
  available: boolean;
}

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[];
  toolCallId?: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface LLMResponse {
  content: string;
  toolCalls?: ToolCall[] | undefined;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  } | undefined;
  finishReason: 'stop' | 'length' | 'tool_calls' | 'content_filter';
}

// Tool Types
export interface Tool {
  name: string;
  description: string;
  parameters: z.ZodSchema;
  execute: (params: unknown, context: AgentContext) => Promise<ToolResult>;
  parallel: boolean;
  dangerous: boolean;
}

export abstract class BaseTool<T = unknown> implements Tool {
  abstract name: string;
  abstract description: string;
  abstract parameters: z.ZodSchema<any>;
  abstract parallel: boolean;
  abstract dangerous: boolean;

  protected abstract executeTyped(params: T, context: AgentContext): Promise<ToolResult>;

  async execute(params: unknown, context: AgentContext): Promise<ToolResult> {
    try {
      const validatedParams = this.parameters.parse(params) as T;
      return await this.executeTyped(validatedParams, context);
    } catch (error) {
      return {
        success: false,
        error: `Parameter validation failed: ${(error as Error).message}`,
      };
    }
  }
}

export interface ToolResult {
  success: boolean;
  data?: unknown | undefined;
  error?: string | undefined;
  metadata?: Record<string, unknown> | undefined;
}

// Configuration Types
export const ConfigSchema = z.object({
  providers: z.array(z.object({
    name: z.string(),
    type: z.enum(['openai', 'anthropic', 'google', 'mistral', 'deepseek', 'ollama']),
    apiKey: z.string().optional(),
    baseUrl: z.string().optional(),
    model: z.string(),
    maxTokens: z.number().default(4000),
    temperature: z.number().min(0).max(2).default(0.7),
    priority: z.number().default(1).optional(),
    available: z.boolean().default(false),
  })),
  session: z.object({
    persistContext: z.boolean().default(true),
    maxHistorySize: z.number().default(1000),
    autoSave: z.boolean().default(true),
  }),
  ui: z.object({
    theme: z.enum(['dark', 'light', 'auto']).default('auto'),
    verbosity: z.enum(['silent', 'normal', 'verbose', 'debug']).default('normal'),
    showProgress: z.boolean().default(true),
    colors: z.boolean().default(true),
  }),
  tools: z.object({
    allowDangerous: z.boolean().default(false),
    maxParallel: z.number().default(5),
    timeout: z.number().default(30000),
  }),
});

export type Config = z.infer<typeof ConfigSchema>;

// Session Types
export interface Session {
  id: string;
  name: string;
  createdAt: Date;
  lastAccessed: Date;
  context: AgentContext;
  config: Config;
}

// Progress and UI Types
export interface ProgressUpdate {
  taskId: string;
  progress: number;
  message: string;
  status: 'running' | 'completed' | 'failed';
}

export interface UITheme {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

// Error Types
export class AgentError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AgentError';
  }
}

export class ToolError extends Error {
  constructor(
    message: string,
    public toolName: string,
    public context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'ToolError';
  }
}

export class ProviderError extends Error {
  constructor(
    message: string,
    public provider: string,
    public context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'ProviderError';
  }
}
