import axios, { AxiosInstance } from 'axios';
import { Logger } from 'winston';
import { LLMProvider, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { ProviderInterface, GenerationOptions } from './llm-provider-manager';

export class OllamaProvider implements ProviderInterface {
  public readonly name: string;
  public readonly type: LLMProvider['type'] = 'ollama';
  
  private client: AxiosInstance;
  private config: <PERSON><PERSON><PERSON><PERSON>;
  private logger: Logger;

  constructor(config: <PERSON><PERSON>rovider, logger: Logger) {
    this.name = config.name;
    this.config = config;
    this.logger = logger;

    this.client = axios.create({
      baseURL: config.baseUrl || 'http://localhost:11434',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 60000, // Ollama can be slower
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.client.get('/api/tags');
      return true;
    } catch (error) {
      this.logger.warn(`Ollama provider ${this.name} not available`, { error });
      return false;
    }
  }

  async generateResponse(
    messages: LLMMessage[], 
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    try {
      const prompt = this.convertMessages(messages);
      
      const response = await this.client.post('/api/generate', {
        model: this.config.model,
        prompt,
        stream: false,
        options: {
          temperature: options.temperature ?? this.config.temperature,
          num_predict: options.maxTokens ?? this.config.maxTokens,
          top_p: options.topP,
          stop: options.stop,
        },
      });

      return {
        content: response.data.response || '',
        usage: {
          promptTokens: response.data.prompt_eval_count || 0,
          completionTokens: response.data.eval_count || 0,
          totalTokens: (response.data.prompt_eval_count || 0) + (response.data.eval_count || 0),
        },
        finishReason: response.data.done ? 'stop' : 'length',
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new ProviderError(
          `Ollama API error: ${error.response?.data?.error || error.message}`,
          this.name,
          { status: error.response?.status }
        );
      }
      throw new ProviderError(`Ollama request failed: ${(error as Error).message}`, this.name);
    }
  }

  private convertMessages(messages: LLMMessage[]): string {
    return messages.map(msg => {
      if (msg.role === 'system') {
        return `System: ${msg.content}`;
      } else if (msg.role === 'user') {
        return `Human: ${msg.content}`;
      } else if (msg.role === 'assistant') {
        return `Assistant: ${msg.content}`;
      }
      return msg.content;
    }).join('\n\n');
  }

  estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  getMaxTokens(): number {
    return this.config.maxTokens;
  }
}
