import { Logger } from 'winston';
import { z } from 'zod';
import { promises as fs } from 'fs';
import path from 'path';
import { <PERSON>Tool, <PERSON><PERSON><PERSON><PERSON>ult, AgentContext, ToolError } from '@/types';

const CodeAnalysisToolSchema = z.discriminatedUnion('operation', [
  z.object({
    operation: z.literal('analyze_file'),
    filePath: z.string(),
    analysisType: z.enum(['syntax', 'complexity', 'dependencies', 'security', 'performance', 'all']).default('all'),
  }),
  z.object({
    operation: z.literal('analyze_directory'),
    directoryPath: z.string(),
    filePattern: z.string().default('**/*.{js,ts,jsx,tsx,py,java,cpp,c,cs,php,rb,go,rs}'),
    maxFiles: z.number().min(1).max(1000).default(100),
    analysisType: z.enum(['overview', 'detailed', 'metrics']).default('overview'),
  }),
  z.object({
    operation: z.literal('find_issues'),
    path: z.string(),
    issueTypes: z.array(z.enum(['syntax', 'logic', 'security', 'performance', 'style'])).default(['syntax', 'logic']),
    severity: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  }),
  z.object({
    operation: z.literal('suggest_improvements'),
    filePath: z.string(),
    focusAreas: z.array(z.enum(['performance', 'readability', 'maintainability', 'security'])).default(['performance', 'readability']),
  }),
  z.object({
    operation: z.literal('generate_documentation'),
    filePath: z.string(),
    docType: z.enum(['api', 'readme', 'comments', 'jsdoc']).default('comments'),
  }),
]);

type CodeAnalysisToolParams = z.infer<typeof CodeAnalysisToolSchema>;

interface CodeMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  maintainabilityIndex: number;
  technicalDebt: number;
  testCoverage?: number;
}

interface CodeIssue {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  line?: number;
  column?: number;
  rule?: string;
  suggestion?: string;
}

export class CodeAnalysisTool extends BaseTool<CodeAnalysisToolParams> {
  public readonly name = 'code_analysis';
  public readonly description = 'Advanced code analysis including syntax checking, complexity analysis, security scanning, and improvement suggestions';
  public readonly parameters = CodeAnalysisToolSchema;
  public readonly parallel = true;
  public readonly dangerous = false;

  private readonly logger: Logger;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
  }

  protected async executeTyped(params: CodeAnalysisToolParams, context: AgentContext): Promise<ToolResult> {
    this.logger.info('Executing code analysis', { operation: params.operation });

    try {
      switch (params.operation) {
        case 'analyze_file':
          return await this.analyzeFile(params, context);
        case 'analyze_directory':
          return await this.analyzeDirectory(params, context);
        case 'find_issues':
          return await this.findIssues(params, context);
        case 'suggest_improvements':
          return await this.suggestImprovements(params, context);
        case 'generate_documentation':
          return await this.generateDocumentation(params, context);
        default:
          throw new ToolError(`Unknown operation: ${(params as any).operation}`, this.name);
      }
    } catch (error) {
      this.logger.error('Code analysis failed', { error, operation: params.operation });
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  private async analyzeFile(
    params: Extract<CodeAnalysisToolParams, { operation: 'analyze_file' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.filePath, context.workingDirectory);
    
    if (!(await this.fileExists(fullPath))) {
      throw new ToolError(`File not found: ${fullPath}`, this.name);
    }

    const content = await fs.readFile(fullPath, 'utf8');
    const fileExtension = path.extname(fullPath).toLowerCase();
    
    const analysis: any = {
      filePath: fullPath,
      fileSize: content.length,
      language: this.detectLanguage(fileExtension),
      lastModified: (await fs.stat(fullPath)).mtime,
    };

    if (params.analysisType === 'all' || params.analysisType === 'syntax') {
      analysis.syntax = await this.analyzeSyntax(content, fileExtension);
    }

    if (params.analysisType === 'all' || params.analysisType === 'complexity') {
      analysis.complexity = await this.analyzeComplexity(content, fileExtension);
    }

    if (params.analysisType === 'all' || params.analysisType === 'dependencies') {
      analysis.dependencies = await this.analyzeDependencies(content, fileExtension);
    }

    if (params.analysisType === 'all' || params.analysisType === 'security') {
      analysis.security = await this.analyzeSecurity(content, fileExtension);
    }

    if (params.analysisType === 'all' || params.analysisType === 'performance') {
      analysis.performance = await this.analyzePerformance(content, fileExtension);
    }

    return {
      success: true,
      data: analysis,
    };
  }

  private async analyzeDirectory(
    params: Extract<CodeAnalysisToolParams, { operation: 'analyze_directory' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.directoryPath, context.workingDirectory);
    
    if (!(await this.directoryExists(fullPath))) {
      throw new ToolError(`Directory not found: ${fullPath}`, this.name);
    }

    const files = await this.findCodeFiles(fullPath, params.filePattern, params.maxFiles);
    
    const analysis: any = {
      directoryPath: fullPath,
      totalFiles: files.length,
      languages: new Set<string>(),
      totalLinesOfCode: 0,
      averageComplexity: 0,
      issues: [],
    };

    let totalComplexity = 0;
    let filesWithComplexity = 0;

    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const fileExtension = path.extname(file).toLowerCase();
        const language = this.detectLanguage(fileExtension);
        
        analysis.languages.add(language);
        analysis.totalLinesOfCode += content.split('\n').length;

        if (params.analysisType === 'detailed' || params.analysisType === 'metrics') {
          const complexity = await this.analyzeComplexity(content, fileExtension);
          if (complexity.cyclomaticComplexity > 0) {
            totalComplexity += complexity.cyclomaticComplexity;
            filesWithComplexity++;
          }

          const issues = await this.findFileIssues(content, fileExtension);
          analysis.issues.push(...issues.map((issue: CodeIssue) => ({
            ...issue,
            file: path.relative(fullPath, file),
          })));
        }
      } catch (error) {
        this.logger.warn('Failed to analyze file', { file, error });
      }
    }

    analysis.languages = Array.from(analysis.languages);
    analysis.averageComplexity = filesWithComplexity > 0 ? totalComplexity / filesWithComplexity : 0;
    analysis.issuesSummary = this.summarizeIssues(analysis.issues);

    return {
      success: true,
      data: analysis,
    };
  }

  private async findIssues(
    params: Extract<CodeAnalysisToolParams, { operation: 'find_issues' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.path, context.workingDirectory);
    const isDirectory = await this.directoryExists(fullPath);
    const isFile = await this.fileExists(fullPath);

    if (!isDirectory && !isFile) {
      throw new ToolError(`Path not found: ${fullPath}`, this.name);
    }

    const issues: CodeIssue[] = [];

    if (isFile) {
      const content = await fs.readFile(fullPath, 'utf8');
      const fileExtension = path.extname(fullPath).toLowerCase();
      const fileIssues = await this.findFileIssues(content, fileExtension, params.issueTypes);
      issues.push(...fileIssues);
    } else {
      const files = await this.findCodeFiles(fullPath, '**/*.{js,ts,jsx,tsx,py,java,cpp,c,cs,php,rb,go,rs}', 50);
      
      for (const file of files) {
        try {
          const content = await fs.readFile(file, 'utf8');
          const fileExtension = path.extname(file).toLowerCase();
          const fileIssues = await this.findFileIssues(content, fileExtension, params.issueTypes);
          issues.push(...fileIssues.map(issue => ({
            ...issue,
            file: path.relative(fullPath, file),
          })));
        } catch (error) {
          this.logger.warn('Failed to analyze file for issues', { file, error });
        }
      }
    }

    const filteredIssues = issues.filter(issue => {
      const severityLevels = ['low', 'medium', 'high', 'critical'];
      const minSeverityIndex = severityLevels.indexOf(params.severity);
      const issueSeverityIndex = severityLevels.indexOf(issue.severity);
      return issueSeverityIndex >= minSeverityIndex;
    });

    return {
      success: true,
      data: {
        path: fullPath,
        totalIssues: filteredIssues.length,
        issues: filteredIssues,
        summary: this.summarizeIssues(filteredIssues),
      },
    };
  }

  private async suggestImprovements(
    params: Extract<CodeAnalysisToolParams, { operation: 'suggest_improvements' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.filePath, context.workingDirectory);
    
    if (!(await this.fileExists(fullPath))) {
      throw new ToolError(`File not found: ${fullPath}`, this.name);
    }

    const content = await fs.readFile(fullPath, 'utf8');
    const fileExtension = path.extname(fullPath).toLowerCase();
    
    const suggestions: any[] = [];

    for (const focusArea of params.focusAreas) {
      const areaSuggestions = await this.generateSuggestions(content, fileExtension, focusArea);
      suggestions.push(...areaSuggestions);
    }

    return {
      success: true,
      data: {
        filePath: fullPath,
        focusAreas: params.focusAreas,
        suggestions,
        totalSuggestions: suggestions.length,
      },
    };
  }

  private async generateDocumentation(
    params: Extract<CodeAnalysisToolParams, { operation: 'generate_documentation' }>,
    context: AgentContext
  ): Promise<ToolResult> {
    const fullPath = this.resolvePath(params.filePath, context.workingDirectory);
    
    if (!(await this.fileExists(fullPath))) {
      throw new ToolError(`File not found: ${fullPath}`, this.name);
    }

    const content = await fs.readFile(fullPath, 'utf8');
    const fileExtension = path.extname(fullPath).toLowerCase();
    
    const documentation = await this.generateDocs(content, fileExtension, params.docType);

    return {
      success: true,
      data: {
        filePath: fullPath,
        docType: params.docType,
        documentation,
      },
    };
  }

  // Helper methods for analysis
  private detectLanguage(extension: string): string {
    const languageMap: Record<string, string> = {
      '.js': 'JavaScript',
      '.jsx': 'JavaScript (React)',
      '.ts': 'TypeScript',
      '.tsx': 'TypeScript (React)',
      '.py': 'Python',
      '.java': 'Java',
      '.cpp': 'C++',
      '.c': 'C',
      '.cs': 'C#',
      '.php': 'PHP',
      '.rb': 'Ruby',
      '.go': 'Go',
      '.rs': 'Rust',
    };
    
    return languageMap[extension] || 'Unknown';
  }

  private async analyzeSyntax(content: string, extension: string): Promise<any> {
    // Basic syntax analysis - would be enhanced with actual parsers
    const lines = content.split('\n');
    const issues: CodeIssue[] = [];
    
    // Simple checks for common syntax issues
    lines.forEach((line, index) => {
      if (line.trim().endsWith(';') && (extension === '.py' || extension === '.rb')) {
        issues.push({
          type: 'syntax',
          severity: 'low',
          message: 'Unnecessary semicolon',
          line: index + 1,
          suggestion: 'Remove semicolon',
        });
      }
    });

    return {
      valid: issues.length === 0,
      issues,
      linesAnalyzed: lines.length,
    };
  }

  private async analyzeComplexity(content: string, _extension: string): Promise<CodeMetrics> {
    const lines = content.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    
    // Simple complexity calculation
    const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);
    const maintainabilityIndex = Math.max(0, 171 - 5.2 * Math.log(nonEmptyLines.length) - 0.23 * cyclomaticComplexity);
    
    return {
      linesOfCode: nonEmptyLines.length,
      cyclomaticComplexity,
      maintainabilityIndex,
      technicalDebt: Math.max(0, (cyclomaticComplexity - 10) * 0.5),
    };
  }

  private calculateCyclomaticComplexity(content: string): number {
    // Simple cyclomatic complexity calculation
    const complexityKeywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?'];
    let complexity = 1; // Base complexity
    
    complexityKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    });
    
    return complexity;
  }

  private async analyzeDependencies(content: string, extension: string): Promise<any> {
    const dependencies: string[] = [];
    
    // Extract imports/requires based on language
    if (extension === '.js' || extension === '.jsx' || extension === '.ts' || extension === '.tsx') {
      const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
      const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
      
      let match;
      while ((match = importRegex.exec(content)) !== null) {
        dependencies.push(match[1]!);
      }
      while ((match = requireRegex.exec(content)) !== null) {
        dependencies.push(match[1]!);
      }
    }
    
    return {
      totalDependencies: dependencies.length,
      dependencies: [...new Set(dependencies)],
      externalDependencies: dependencies.filter(dep => !dep.startsWith('./')),
    };
  }

  private async analyzeSecurity(content: string, _extension: string): Promise<any> {
    const securityIssues: CodeIssue[] = [];
    
    // Basic security checks
    const securityPatterns = [
      { pattern: /eval\s*\(/, message: 'Use of eval() is dangerous', severity: 'high' as const },
      { pattern: /innerHTML\s*=/, message: 'Direct innerHTML assignment can lead to XSS', severity: 'medium' as const },
      { pattern: /document\.write\s*\(/, message: 'document.write can be dangerous', severity: 'medium' as const },
    ];
    
    securityPatterns.forEach(({ pattern, message, severity }) => {
      const matches = content.match(pattern);
      if (matches) {
        securityIssues.push({
          type: 'security',
          severity,
          message,
          suggestion: 'Consider safer alternatives',
        });
      }
    });
    
    return {
      securityScore: Math.max(0, 100 - securityIssues.length * 10),
      issues: securityIssues,
      recommendations: securityIssues.length > 0 ? ['Review security patterns', 'Use security linting tools'] : [],
    };
  }

  private async analyzePerformance(content: string, _extension: string): Promise<any> {
    const performanceIssues: CodeIssue[] = [];
    
    // Basic performance checks
    if (content.includes('for (') && content.includes('.length')) {
      performanceIssues.push({
        type: 'performance',
        severity: 'low',
        message: 'Consider caching array length in loops',
        suggestion: 'Store array.length in a variable before the loop',
      });
    }
    
    return {
      performanceScore: Math.max(0, 100 - performanceIssues.length * 5),
      issues: performanceIssues,
      suggestions: performanceIssues.map(issue => issue.suggestion).filter(Boolean),
    };
  }

  private async findFileIssues(content: string, extension: string, issueTypes?: string[]): Promise<CodeIssue[]> {
    const issues: CodeIssue[] = [];
    
    if (!issueTypes || issueTypes.includes('syntax')) {
      const syntaxAnalysis = await this.analyzeSyntax(content, extension);
      issues.push(...syntaxAnalysis.issues);
    }
    
    if (!issueTypes || issueTypes.includes('security')) {
      const securityAnalysis = await this.analyzeSecurity(content, extension);
      issues.push(...securityAnalysis.issues);
    }
    
    if (!issueTypes || issueTypes.includes('performance')) {
      const performanceAnalysis = await this.analyzePerformance(content, extension);
      issues.push(...performanceAnalysis.issues);
    }
    
    return issues;
  }

  private async generateSuggestions(content: string, _extension: string, focusArea: string): Promise<any[]> {
    const suggestions: any[] = [];
    
    switch (focusArea) {
      case 'performance':
        if (content.includes('console.log')) {
          suggestions.push({
            type: 'performance',
            message: 'Remove console.log statements in production',
            priority: 'medium',
          });
        }
        break;
      case 'readability': {
        const lines = content.split('\n');
        const longLines = lines.filter(line => line.length > 120);
        if (longLines.length > 0) {
          suggestions.push({
            type: 'readability',
            message: `${longLines.length} lines exceed 120 characters`,
            priority: 'low',
          });
        }
        break;
      }
    }
    
    return suggestions;
  }

  private async generateDocs(_content: string, _extension: string, docType: string): Promise<string> {
    // Basic documentation generation
    switch (docType) {
      case 'comments':
        return '// TODO: Add comprehensive comments\n// This file needs documentation';
      case 'readme':
        return '# Code Documentation\n\nThis file contains implementation details.';
      default:
        return 'Documentation generated for the code file.';
    }
  }

  private summarizeIssues(issues: CodeIssue[]): any {
    const summary = {
      total: issues.length,
      bySeverity: {} as Record<string, number>,
      byType: {} as Record<string, number>,
    };
    
    issues.forEach(issue => {
      summary.bySeverity[issue.severity] = (summary.bySeverity[issue.severity] || 0) + 1;
      summary.byType[issue.type] = (summary.byType[issue.type] || 0) + 1;
    });
    
    return summary;
  }

  private resolvePath(filePath: string, workingDirectory: string): string {
    return path.isAbsolute(filePath) ? filePath : path.resolve(workingDirectory, filePath);
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);
      return stats.isFile();
    } catch {
      return false;
    }
  }

  private async directoryExists(dirPath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(dirPath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  private async findCodeFiles(directory: string, pattern: string, maxFiles: number): Promise<string[]> {
    // Simple file finding - would use glob in production
    const files: string[] = [];
    
    try {
      const entries = await fs.readdir(directory, { withFileTypes: true });
      
      for (const entry of entries) {
        if (files.length >= maxFiles) break;
        
        const fullPath = path.join(directory, entry.name);
        
        if (entry.isFile() && this.matchesPattern(entry.name, pattern)) {
          files.push(fullPath);
        } else if (entry.isDirectory() && !entry.name.startsWith('.')) {
          const subFiles = await this.findCodeFiles(fullPath, pattern, maxFiles - files.length);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      this.logger.warn('Failed to read directory', { directory, error });
    }
    
    return files;
  }

  private matchesPattern(filename: string, _pattern: string): boolean {
    // Simple pattern matching - would use proper glob matching in production
    const extensions = ['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.cs', '.php', '.rb', '.go', '.rs'];
    return extensions.some(ext => filename.endsWith(ext));
  }
}
