import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { ContextMemory, AgentAction } from '@/types';

export interface MemoryPattern {
  id: string;
  pattern: string;
  frequency: number;
  lastSeen: Date;
  context: string[];
  confidence: number;
}

export interface LearningInsight {
  id: string;
  type: 'success_pattern' | 'failure_pattern' | 'optimization' | 'preference';
  description: string;
  evidence: string[];
  confidence: number;
  createdAt: Date;
  applicableContexts: string[];
}

export class MemoryManager extends EventEmitter {
  private readonly logger: Logger;
  private readonly memoryDir: string;
  private memory: ContextMemory;
  private patterns: Map<string, MemoryPattern> = new Map();
  private insights: Map<string, LearningInsight> = new Map();

  constructor(logger: Logger, sessionId: string) {
    super();
    this.logger = logger;
    this.memoryDir = path.join(process.cwd(), '.agent-memory', sessionId);
    this.memory = {
      facts: {},
      patterns: [],
      preferences: {},
      learnings: [],
    };
    this.initializeMemory();
  }

  private async initializeMemory(): Promise<void> {
    try {
      await fs.mkdir(this.memoryDir, { recursive: true });
      await this.loadPersistedMemory();
    } catch (error) {
      this.logger.warn('Failed to initialize memory', { error });
    }
  }

  private async loadPersistedMemory(): Promise<void> {
    try {
      // Load basic memory
      const memoryPath = path.join(this.memoryDir, 'memory.json');
      try {
        const data = await fs.readFile(memoryPath, 'utf8');
        this.memory = JSON.parse(data);
      } catch {
        // Memory file doesn't exist, use defaults
      }

      // Load patterns
      const patternsPath = path.join(this.memoryDir, 'patterns.json');
      try {
        const data = await fs.readFile(patternsPath, 'utf8');
        const patternsArray = JSON.parse(data);
        this.patterns = new Map(patternsArray.map((p: any) => [p.id, {
          ...p,
          lastSeen: new Date(p.lastSeen),
        }]));
      } catch {
        // Patterns file doesn't exist
      }

      // Load insights
      const insightsPath = path.join(this.memoryDir, 'insights.json');
      try {
        const data = await fs.readFile(insightsPath, 'utf8');
        const insightsArray = JSON.parse(data);
        this.insights = new Map(insightsArray.map((i: any) => [i.id, {
          ...i,
          createdAt: new Date(i.createdAt),
        }]));
      } catch {
        // Insights file doesn't exist
      }

      this.logger.info('Memory loaded', {
        facts: Object.keys(this.memory.facts).length,
        patterns: this.patterns.size,
        insights: this.insights.size,
        learnings: this.memory.learnings.length,
      });
    } catch (error) {
      this.logger.warn('Failed to load persisted memory', { error });
    }
  }

  async addFact(key: string, value: unknown, context?: string): Promise<void> {
    this.memory.facts[key] = value;
    
    if (context) {
      await this.analyzeForPatterns(key, value, context);
    }

    this.emit('factAdded', { key, value, context });
    await this.persistMemory();
  }

  async addLearning(learning: string, context?: string): Promise<void> {
    this.memory.learnings.push(learning);
    
    // Limit learnings to prevent memory bloat
    const maxLearnings = 1000;
    if (this.memory.learnings.length > maxLearnings) {
      this.memory.learnings = this.memory.learnings.slice(-maxLearnings);
    }

    if (context) {
      await this.extractInsights(learning, context);
    }

    this.emit('learningAdded', { learning, context });
    await this.persistMemory();
  }

  async setPreference(key: string, value: unknown): Promise<void> {
    this.memory.preferences[key] = value;
    this.emit('preferenceSet', { key, value });
    await this.persistMemory();
  }

  async analyzeActions(actions: AgentAction[]): Promise<void> {
    for (const action of actions) {
      await this.analyzeAction(action);
    }
  }

  private async analyzeAction(action: AgentAction): Promise<void> {
    // Analyze for success patterns
    if (action.success) {
      await this.recordSuccessPattern(action);
    } else {
      await this.recordFailurePattern(action);
    }

    // Analyze performance patterns
    await this.analyzePerformance(action);
  }

  private async recordSuccessPattern(action: AgentAction): Promise<void> {
    const patternKey = `success_${action.type}`;
    const pattern = this.patterns.get(patternKey);
    
    if (pattern) {
      pattern.frequency++;
      pattern.lastSeen = new Date();
      pattern.context.push(JSON.stringify(action.input));
    } else {
      this.patterns.set(patternKey, {
        id: patternKey,
        pattern: `Successful ${action.type} operations`,
        frequency: 1,
        lastSeen: new Date(),
        context: [JSON.stringify(action.input)],
        confidence: 0.5,
      });
    }

    await this.persistPatterns();
  }

  private async recordFailurePattern(action: AgentAction): Promise<void> {
    const patternKey = `failure_${action.type}`;
    const pattern = this.patterns.get(patternKey);
    
    if (pattern) {
      pattern.frequency++;
      pattern.lastSeen = new Date();
      pattern.context.push(action.error || 'Unknown error');
    } else {
      this.patterns.set(patternKey, {
        id: patternKey,
        pattern: `Failed ${action.type} operations`,
        frequency: 1,
        lastSeen: new Date(),
        context: [action.error || 'Unknown error'],
        confidence: 0.5,
      });
    }

    await this.persistPatterns();
  }

  private async analyzePerformance(action: AgentAction): Promise<void> {
    const performanceKey = `performance_${action.type}`;
    
    // Track average performance
    if (!this.memory.facts[performanceKey]) {
      this.memory.facts[performanceKey] = {
        totalDuration: action.duration,
        count: 1,
        average: action.duration,
      };
    } else {
      const perf = this.memory.facts[performanceKey] as any;
      perf.totalDuration += action.duration;
      perf.count++;
      perf.average = perf.totalDuration / perf.count;
    }

    // Generate insights for performance anomalies
    const perf = this.memory.facts[performanceKey] as any;
    if (action.duration > perf.average * 2) {
      await this.generateInsight({
        type: 'optimization',
        description: `${action.type} operation took ${action.duration}ms, significantly longer than average ${perf.average}ms`,
        evidence: [JSON.stringify(action)],
        confidence: 0.7,
        applicableContexts: [action.type],
      });
    }
  }

  private async analyzeForPatterns(key: string, value: unknown, context: string): Promise<void> {
    // Simple pattern analysis - could be enhanced with ML
    const valueStr = String(value);
    
    // Look for repeated values
    const existingFacts = Object.entries(this.memory.facts);
    const similarFacts = existingFacts.filter(([k, v]) => 
      k !== key && String(v) === valueStr
    );

    if (similarFacts.length > 0) {
      const patternId = `repeated_value_${valueStr.slice(0, 20)}`;
      await this.updatePattern(patternId, `Repeated value: ${valueStr}`, context);
    }
  }

  private async updatePattern(id: string, description: string, context: string): Promise<void> {
    const pattern = this.patterns.get(id);
    
    if (pattern) {
      pattern.frequency++;
      pattern.lastSeen = new Date();
      pattern.context.push(context);
      pattern.confidence = Math.min(pattern.confidence + 0.1, 1.0);
    } else {
      this.patterns.set(id, {
        id,
        pattern: description,
        frequency: 1,
        lastSeen: new Date(),
        context: [context],
        confidence: 0.3,
      });
    }

    await this.persistPatterns();
  }

  private async extractInsights(learning: string, context: string): Promise<void> {
    // Simple insight extraction - could be enhanced with NLP
    if (learning.toLowerCase().includes('successful')) {
      await this.generateInsight({
        type: 'success_pattern',
        description: `Success pattern identified: ${learning}`,
        evidence: [learning],
        confidence: 0.6,
        applicableContexts: [context],
      });
    } else if (learning.toLowerCase().includes('failed') || learning.toLowerCase().includes('error')) {
      await this.generateInsight({
        type: 'failure_pattern',
        description: `Failure pattern identified: ${learning}`,
        evidence: [learning],
        confidence: 0.6,
        applicableContexts: [context],
      });
    }
  }

  private async generateInsight(insight: Omit<LearningInsight, 'id' | 'createdAt'>): Promise<void> {
    const id = `insight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullInsight: LearningInsight = {
      ...insight,
      id,
      createdAt: new Date(),
    };

    this.insights.set(id, fullInsight);
    this.emit('insightGenerated', { insight: fullInsight });
    await this.persistInsights();
  }

  getMemory(): ContextMemory {
    return this.memory;
  }

  getPatterns(): MemoryPattern[] {
    return Array.from(this.patterns.values());
  }

  getInsights(): LearningInsight[] {
    return Array.from(this.insights.values());
  }

  getRelevantInsights(context: string): LearningInsight[] {
    return Array.from(this.insights.values()).filter(insight =>
      insight.applicableContexts.includes(context) ||
      insight.description.toLowerCase().includes(context.toLowerCase())
    );
  }

  private async persistMemory(): Promise<void> {
    try {
      const memoryPath = path.join(this.memoryDir, 'memory.json');
      await fs.writeFile(memoryPath, JSON.stringify(this.memory, null, 2));
    } catch (error) {
      this.logger.warn('Failed to persist memory', { error });
    }
  }

  private async persistPatterns(): Promise<void> {
    try {
      const patternsPath = path.join(this.memoryDir, 'patterns.json');
      const patternsArray = Array.from(this.patterns.values());
      await fs.writeFile(patternsPath, JSON.stringify(patternsArray, null, 2));
    } catch (error) {
      this.logger.warn('Failed to persist patterns', { error });
    }
  }

  private async persistInsights(): Promise<void> {
    try {
      const insightsPath = path.join(this.memoryDir, 'insights.json');
      const insightsArray = Array.from(this.insights.values());
      await fs.writeFile(insightsPath, JSON.stringify(insightsArray, null, 2));
    } catch (error) {
      this.logger.warn('Failed to persist insights', { error });
    }
  }

  async cleanup(): Promise<void> {
    // Clean up old patterns and insights
    const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days

    // Remove old patterns with low confidence
    for (const [id, pattern] of this.patterns.entries()) {
      if (pattern.lastSeen < cutoffDate && pattern.confidence < 0.5) {
        this.patterns.delete(id);
      }
    }

    // Remove old insights with low confidence
    for (const [id, insight] of this.insights.entries()) {
      if (insight.createdAt < cutoffDate && insight.confidence < 0.5) {
        this.insights.delete(id);
      }
    }

    await Promise.all([
      this.persistPatterns(),
      this.persistInsights(),
    ]);

    this.logger.info('Memory cleanup completed');
  }

  async shutdown(): Promise<void> {
    await Promise.all([
      this.persistMemory(),
      this.persistPatterns(),
      this.persistInsights(),
    ]);
    
    this.removeAllListeners();
    this.logger.info('Memory manager shutdown');
  }
}
