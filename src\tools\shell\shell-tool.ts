import { spawn } from 'child_process';
import { Logger } from 'winston';
import { z } from 'zod';
import { BaseTool, <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentContext, ToolError } from '@/types';

const ShellToolSchema = z.object({
  command: z.string().min(1, 'Command cannot be empty'),
  workingDirectory: z.string().optional(),
  timeout: z.number().min(1000).max(300000).default(30000), // 1s to 5min
  shell: z.string().optional(),
  env: z.record(z.string()).optional(),
  input: z.string().optional(),
  captureOutput: z.boolean().default(true),
  allowDangerous: z.boolean().default(false),
});

type ShellToolParams = z.infer<typeof ShellToolSchema>;

export class ShellTool extends BaseTool<ShellToolParams> {
  public readonly name = 'execute_shell_command';
  public readonly description = 'Execute shell commands with full system access including read, write, create, delete, move, copy, chmod, and permissions management';
  public readonly parameters = ShellToolSchema;
  public readonly parallel = true;
  public readonly dangerous = true;

  private readonly logger: Logger;
  private readonly dangerousCommands = new Set([
    'rm -rf /',
    'del /f /s /q C:\\',
    'format',
    'fdisk',
    'mkfs',
    'dd if=/dev/zero',
    'shutdown',
    'reboot',
    'halt',
    'poweroff',
    'init 0',
    'init 6',
  ]);

  constructor(logger: Logger) {
    super();
    this.logger = logger;
  }

  protected async executeTyped(params: ShellToolParams, context: AgentContext): Promise<ToolResult> {
    this.logger.info('Executing shell command', { 
      command: params.command,
      workingDirectory: params.workingDirectory || context.workingDirectory 
    });

    try {
      // Security check for dangerous commands
      if (!params.allowDangerous && this.isDangerousCommand(params.command)) {
        throw new ToolError(
          `Dangerous command blocked: ${params.command}`,
          this.name,
          { command: params.command }
        );
      }

      const workingDir = params.workingDirectory || context.workingDirectory;
      const environment = { ...process.env, ...params.env };

      // Execute command with timeout
      const result = await this.executeWithTimeout(
        params.command,
        workingDir,
        environment,
        params.timeout,
        params.input
      );

      return {
        success: result.exitCode === 0,
        data: {
          stdout: result.stdout,
          stderr: result.stderr,
          exitCode: result.exitCode,
          command: params.command,
          workingDirectory: workingDir,
          duration: result.duration,
        },
        error: result.exitCode !== 0 ? result.stderr || 'Command failed' : undefined,
        metadata: {
          platform: context.environment.platform,
          shell: params.shell || context.environment.shell,
          timeout: params.timeout,
        },
      };
    } catch (error) {
      this.logger.error('Shell command execution failed', { 
        error, 
        command: params.command 
      });

      return {
        success: false,
        error: (error as Error).message,
        metadata: {
          command: params.command,
          workingDirectory: params.workingDirectory || context.workingDirectory,
        },
      };
    }
  }

  private async executeWithTimeout(
    command: string,
    workingDirectory: string,
    env: NodeJS.ProcessEnv,
    timeout: number,
    input?: string
  ): Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
    duration: number;
  }> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const child = spawn(command, [], {
        cwd: workingDirectory,
        env,
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe'],
      });

      let stdout = '';
      let stderr = '';
      let isResolved = false;

      // Set up timeout
      const timeoutId = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          child.kill('SIGTERM');
          
          // Force kill after 5 seconds
          setTimeout(() => {
            if (!child.killed) {
              child.kill('SIGKILL');
            }
          }, 5000);

          reject(new Error(`Command timed out after ${timeout}ms`));
        }
      }, timeout);

      // Handle stdout
      child.stdout?.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      // Handle stderr
      child.stderr?.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      // Handle process exit
      child.on('close', (code) => {
        if (!isResolved) {
          isResolved = true;
          clearTimeout(timeoutId);
          
          resolve({
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            exitCode: code || 0,
            duration: Date.now() - startTime,
          });
        }
      });

      // Handle errors
      child.on('error', (error) => {
        if (!isResolved) {
          isResolved = true;
          clearTimeout(timeoutId);
          reject(error);
        }
      });

      // Send input if provided
      if (input) {
        child.stdin?.write(input);
        child.stdin?.end();
      }
    });
  }

  private isDangerousCommand(command: string): boolean {
    const normalizedCommand = command.toLowerCase().trim();
    
    // Check against known dangerous commands
    for (const dangerous of this.dangerousCommands) {
      if (normalizedCommand.includes(dangerous.toLowerCase())) {
        return true;
      }
    }

    // Check for patterns that could be dangerous
    const dangerousPatterns = [
      /rm\s+.*-rf\s+\//, // rm -rf /
      /del\s+.*\/[fs]\s+.*\/[sq]/, // Windows delete with force/recursive
      /format\s+[a-z]:/i, // Format drive
      /dd\s+if=.*of=/, // dd command
      />\s*\/dev\/sd[a-z]/, // Writing to disk devices
      /chmod\s+777\s+\//, // Dangerous permissions on root
    ];

    return dangerousPatterns.some(pattern => pattern.test(normalizedCommand));
  }

  // Utility methods for common operations
  async readFile(filePath: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `type "${filePath}"`
      : `cat "${filePath}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 10000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async writeFile(filePath: string, content: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `echo "${content}" > "${filePath}"`
      : `echo "${content}" > "${filePath}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 10000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async createDirectory(dirPath: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `mkdir "${dirPath}"`
      : `mkdir -p "${dirPath}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 5000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async deleteFile(filePath: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `del "${filePath}"`
      : `rm "${filePath}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 5000,
      captureOutput: true,
      allowDangerous: true,
    }, context);
  }

  async copyFile(source: string, destination: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `copy "${source}" "${destination}"`
      : `cp "${source}" "${destination}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 10000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async moveFile(source: string, destination: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `move "${source}" "${destination}"`
      : `mv "${source}" "${destination}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 10000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async changePermissions(filePath: string, permissions: string, context: AgentContext): Promise<ToolResult> {
    if (process.platform === 'win32') {
      // Windows doesn't have chmod, use icacls instead
      const command = `icacls "${filePath}" /grant Everyone:F`;
      return this.execute({
        command,
        workingDirectory: context.workingDirectory,
        timeout: 5000,
        captureOutput: true,
        allowDangerous: false,
      }, context);
    } else {
      const command = `chmod ${permissions} "${filePath}"`;
      return this.execute({
        command,
        workingDirectory: context.workingDirectory,
        timeout: 5000,
        captureOutput: true,
        allowDangerous: false,
      }, context);
    }
  }

  async listFiles(directory: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `dir "${directory}" /b`
      : `ls -la "${directory}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 10000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async findFiles(pattern: string, directory: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `dir "${directory}\\${pattern}" /s /b`
      : `find "${directory}" -name "${pattern}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 30000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }

  async grepFiles(pattern: string, filePath: string, context: AgentContext): Promise<ToolResult> {
    const command = process.platform === 'win32'
      ? `findstr "${pattern}" "${filePath}"`
      : `grep -n "${pattern}" "${filePath}"`;

    return this.execute({
      command,
      workingDirectory: context.workingDirectory,
      timeout: 10000,
      captureOutput: true,
      allowDangerous: false,
    }, context);
  }
}
