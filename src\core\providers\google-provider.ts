import { GoogleGenerativeA<PERSON> } from '@google/generative-ai';
import { Logger } from 'winston';
import { <PERSON><PERSON>rovider, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { ProviderInterface, GenerationOptions } from './llm-provider-manager';

export class GoogleProvider implements ProviderInterface {
  public readonly name: string;
  public readonly type: LLMProvider['type'] = 'google';
  
  private client: GoogleGenerativeAI;
  private config: <PERSON><PERSON><PERSON><PERSON>;
  private logger: Logger;

  constructor(config: <PERSON><PERSON>rovider, logger: Logger) {
    this.name = config.name;
    this.config = config;
    this.logger = logger;

    if (!config.apiKey) {
      throw new ProviderError('Google API key is required', config.name);
    }

    this.client = new GoogleGenerativeAI(config.apiKey);
  }

  async isAvailable(): Promise<boolean> {
    try {
      const model = this.client.getGenerativeModel({ model: this.config.model });
      await model.generateContent('test');
      return true;
    } catch (error) {
      this.logger.warn(`Google provider ${this.name} not available`, { error });
      return false;
    }
  }

  async generateResponse(
    messages: LLMMessage[], 
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    try {
      const model = this.client.getGenerativeModel({
        model: this.config.model,
        generationConfig: {
          temperature: options.temperature ?? this.config.temperature,
          maxOutputTokens: options.maxTokens ?? this.config.maxTokens,
          ...(options.topP !== undefined && { topP: options.topP }),
        },
      });

      const prompt = this.convertMessages(messages);
      const result = await model.generateContent(prompt);
      const response = await result.response;

      return {
        content: response.text(),
        usage: {
          promptTokens: result.response.usageMetadata?.promptTokenCount || 0,
          completionTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: result.response.usageMetadata?.totalTokenCount || 0,
        },
        finishReason: 'stop',
      };
    } catch (error) {
      throw new ProviderError(`Google request failed: ${(error as Error).message}`, this.name);
    }
  }

  private convertMessages(messages: LLMMessage[]): string {
    return messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
  }

  estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  getMaxTokens(): number {
    return this.config.maxTokens;
  }
}
