import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { AgentTask, ToolResult } from '@/types';

export interface AuditEvent {
  id: string;
  timestamp: Date;
  type: 'task_started' | 'task_completed' | 'task_failed' | 'tool_executed' | 'error_occurred' | 'security_event' | 'performance_event';
  severity: 'info' | 'warn' | 'error' | 'critical';
  source: string;
  data: Record<string, unknown>;
  userId?: string;
  sessionId: string;
  correlationId?: string;
}

export interface AuditQuery {
  startDate?: Date;
  endDate?: Date;
  type?: string[];
  severity?: string[];
  source?: string;
  sessionId?: string;
  limit?: number;
  offset?: number;
}

export class AuditLogger extends EventEmitter {
  private readonly logger: Logger;
  private readonly auditDir: string;
  private readonly maxFileSize: number;
  private readonly maxFiles: number;
  private currentLogFile?: string;
  private eventBuffer: AuditEvent[] = [];
  private flushInterval?: NodeJS.Timeout;

  constructor(
    logger: Logger,
    auditDir?: string,
    options: {
      maxFileSize?: number;
      maxFiles?: number;
      flushIntervalMs?: number;
    } = {}
  ) {
    super();
    this.logger = logger;
    this.auditDir = auditDir || path.join(process.cwd(), '.agent-audit');
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 100;
    
    this.initializeAuditSystem();
    
    // Start periodic flush
    if (options.flushIntervalMs) {
      this.flushInterval = setInterval(() => {
        this.flushBuffer().catch(error => 
          this.logger.warn('Failed to flush audit buffer', { error })
        );
      }, options.flushIntervalMs);
    }
  }

  private async initializeAuditSystem(): Promise<void> {
    try {
      await fs.mkdir(this.auditDir, { recursive: true });
      this.currentLogFile = await this.getCurrentLogFile();
    } catch (error) {
      this.logger.error('Failed to initialize audit system', { error });
    }
  }

  private async getCurrentLogFile(): Promise<string> {
    const today = new Date().toISOString().split('T')[0];
    const baseFileName = `audit-${today}`;
    let fileIndex = 1;
    let fileName = `${baseFileName}-${fileIndex.toString().padStart(3, '0')}.jsonl`;
    let filePath = path.join(this.auditDir, fileName);

    try {
      while (await this.fileExists(filePath)) {
        const stats = await fs.stat(filePath);
        if (stats.size < this.maxFileSize) {
          return filePath;
        }
        fileIndex++;
        fileName = `${baseFileName}-${fileIndex.toString().padStart(3, '0')}.jsonl`;
        filePath = path.join(this.auditDir, fileName);
      }
    } catch (error) {
      this.logger.warn('Error checking audit file', { error, filePath });
    }

    return filePath;
  }

  async logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
    const auditEvent: AuditEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date(),
    };

    this.eventBuffer.push(auditEvent);
    this.emit('eventLogged', auditEvent);

    // Flush immediately for critical events
    if (event.severity === 'critical') {
      await this.flushBuffer();
    }
  }

  async logTaskStarted(task: AgentTask, sessionId: string): Promise<void> {
    await this.logEvent({
      type: 'task_started',
      severity: 'info',
      source: 'autonomous_agent',
      sessionId,
      data: {
        taskId: task.id,
        taskType: task.type,
        description: task.description,
        priority: task.priority,
        dependencies: task.dependencies,
      },
    });
  }

  async logTaskCompleted(task: AgentTask, result: ToolResult, sessionId: string): Promise<void> {
    await this.logEvent({
      type: 'task_completed',
      severity: 'info',
      source: 'autonomous_agent',
      sessionId,
      data: {
        taskId: task.id,
        taskType: task.type,
        description: task.description,
        success: result.success,
        duration: task.updatedAt.getTime() - task.createdAt.getTime(),
        resultData: result.data,
      },
    });
  }

  async logTaskFailed(task: AgentTask, error: Error, sessionId: string): Promise<void> {
    await this.logEvent({
      type: 'task_failed',
      severity: 'error',
      source: 'autonomous_agent',
      sessionId,
      data: {
        taskId: task.id,
        taskType: task.type,
        description: task.description,
        error: error.message,
        stack: error.stack,
        duration: task.updatedAt.getTime() - task.createdAt.getTime(),
      },
    });
  }

  async logToolExecution(
    toolName: string,
    parameters: unknown,
    result: ToolResult,
    sessionId: string
  ): Promise<void> {
    await this.logEvent({
      type: 'tool_executed',
      severity: result.success ? 'info' : 'warn',
      source: 'tool_registry',
      sessionId,
      data: {
        toolName,
        parameters,
        success: result.success,
        error: result.error,
        executionTime: result.metadata?.['executionTime'],
      },
    });
  }

  async logSecurityEvent(
    eventType: string,
    description: string,
    details: Record<string, unknown>,
    sessionId: string
  ): Promise<void> {
    await this.logEvent({
      type: 'security_event',
      severity: 'warn',
      source: 'security_monitor',
      sessionId,
      data: {
        eventType,
        description,
        ...details,
      },
    });
  }

  async logPerformanceEvent(
    operation: string,
    duration: number,
    details: Record<string, unknown>,
    sessionId: string
  ): Promise<void> {
    const severity = duration > 10000 ? 'warn' : 'info'; // Warn if operation takes > 10s

    await this.logEvent({
      type: 'performance_event',
      severity,
      source: 'performance_monitor',
      sessionId,
      data: {
        operation,
        duration,
        ...details,
      },
    });
  }

  async queryEvents(query: AuditQuery): Promise<AuditEvent[]> {
    const events: AuditEvent[] = [];
    const files = await this.getAuditFiles();
    
    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const lines = content.trim().split('\n').filter(line => line.trim());
        
        for (const line of lines) {
          try {
            const event = JSON.parse(line) as AuditEvent;
            event.timestamp = new Date(event.timestamp);
            
            if (this.matchesQuery(event, query)) {
              events.push(event);
            }
          } catch (parseError) {
            this.logger.warn('Failed to parse audit event', { parseError, line });
          }
        }
      } catch (error) {
        this.logger.warn('Failed to read audit file', { error, file });
      }
    }

    // Sort by timestamp (newest first)
    events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || 100;
    return events.slice(offset, offset + limit);
  }

  async getAuditStatistics(sessionId?: string): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    recentErrors: number;
    averageTaskDuration: number;
  }> {
    const query: AuditQuery = sessionId ? { sessionId } : {};
    const events = await this.queryEvents({ ...query, limit: 10000 });

    const stats = {
      totalEvents: events.length,
      eventsByType: {} as Record<string, number>,
      eventsBySeverity: {} as Record<string, number>,
      recentErrors: 0,
      averageTaskDuration: 0,
    };

    let totalDuration = 0;
    let taskCount = 0;
    const recentCutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours

    for (const event of events) {
      // Count by type
      stats.eventsByType[event.type] = (stats.eventsByType[event.type] || 0) + 1;
      
      // Count by severity
      stats.eventsBySeverity[event.severity] = (stats.eventsBySeverity[event.severity] || 0) + 1;
      
      // Count recent errors
      if (event.severity === 'error' && event.timestamp > recentCutoff) {
        stats.recentErrors++;
      }
      
      // Calculate average task duration
      if (event.type === 'task_completed' && typeof event.data['duration'] === 'number') {
        totalDuration += event.data['duration'] as number;
        taskCount++;
      }
    }

    stats.averageTaskDuration = taskCount > 0 ? totalDuration / taskCount : 0;

    return stats;
  }

  private async flushBuffer(): Promise<void> {
    if (this.eventBuffer.length === 0) {
      return;
    }

    try {
      if (!this.currentLogFile) {
        this.currentLogFile = await this.getCurrentLogFile();
      }

      const events = this.eventBuffer.splice(0);
      const logLines = events.map(event => JSON.stringify(event)).join('\n') + '\n';
      
      await fs.appendFile(this.currentLogFile, logLines);
      
      // Check if we need to rotate the log file
      const stats = await fs.stat(this.currentLogFile);
      if (stats.size >= this.maxFileSize) {
        this.currentLogFile = await this.getCurrentLogFile();
      }
    } catch (error) {
      this.logger.error('Failed to flush audit buffer', { error });
      // Put events back in buffer for retry
      this.eventBuffer.unshift(...this.eventBuffer);
    }
  }

  private async getAuditFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.auditDir);
      const auditFiles = files
        .filter(file => file.startsWith('audit-') && file.endsWith('.jsonl'))
        .map(file => path.join(this.auditDir, file));
      
      // Sort by modification time (newest first)
      const filesWithStats = await Promise.all(
        auditFiles.map(async file => ({
          file,
          mtime: (await fs.stat(file)).mtime,
        }))
      );
      
      return filesWithStats
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime())
        .map(item => item.file);
    } catch (error) {
      this.logger.warn('Failed to get audit files', { error });
      return [];
    }
  }

  private matchesQuery(event: AuditEvent, query: AuditQuery): boolean {
    if (query.startDate && event.timestamp < query.startDate) {
      return false;
    }
    
    if (query.endDate && event.timestamp > query.endDate) {
      return false;
    }
    
    if (query.type && !query.type.includes(event.type)) {
      return false;
    }
    
    if (query.severity && !query.severity.includes(event.severity)) {
      return false;
    }
    
    if (query.source && event.source !== query.source) {
      return false;
    }
    
    if (query.sessionId && event.sessionId !== query.sessionId) {
      return false;
    }
    
    return true;
  }

  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async cleanup(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<number> {
    const cutoffDate = new Date(Date.now() - maxAge);
    const files = await this.getAuditFiles();
    let deletedCount = 0;

    // Delete files older than maxAge
    for (const file of files) {
      try {
        const stats = await fs.stat(file);
        if (stats.mtime < cutoffDate) {
          await fs.unlink(file);
          deletedCount++;
          this.logger.info('Deleted old audit file', { file });
        }
      } catch (error) {
        this.logger.warn('Failed to delete audit file', { error, file });
      }
    }

    // Enforce maxFiles limit
    const remainingFiles = await this.getAuditFiles();
    if (remainingFiles.length > this.maxFiles) {
      const filesToDelete = remainingFiles.slice(this.maxFiles);
      for (const file of filesToDelete) {
        try {
          await fs.unlink(file);
          deletedCount++;
          this.logger.info('Deleted excess audit file', { file });
        } catch (error) {
          this.logger.warn('Failed to delete excess audit file', { error, file });
        }
      }
    }

    return deletedCount;
  }

  async shutdown(): Promise<void> {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    
    await this.flushBuffer();
    this.removeAllListeners();
    this.logger.info('Audit logger shutdown');
  }
}
